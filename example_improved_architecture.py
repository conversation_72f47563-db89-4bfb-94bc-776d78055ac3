"""
Example implementation showing improved architecture for policy decision workflow.
This demonstrates key architectural patterns and improvements.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, Protocol
import asyncio
import logging
from contextlib import asynccontextmanager
from pocketflow import AsyncNode, AsyncFlow

# ============================================================================
# 1. CONFIGURATION MANAGEMENT
# ============================================================================

@dataclass
class WorkflowConfig:
    """Centralized configuration management"""
    deepseek_api_key: str
    tavily_api_key: Optional[str] = None
    max_tokens: int = 2048
    temperature: float = 0.7
    max_retries: int = 2
    retry_wait: int = 5
    query_generation_count: int = 1
    max_search_count: int = 1
    
    @classmethod
    def from_env(cls) -> 'WorkflowConfig':
        import os
        return cls(
            deepseek_api_key=os.getenv('DEEPSEEK_API_KEY', ''),
            tavily_api_key=os.getenv('TAVILY_API_KEY'),
            query_generation_count=int(os.getenv('QUERY_GENERATION_COUNT', '1')),
            max_search_count=int(os.getenv('MAX_SEARCH_COUNT', '1'))
        )

# ============================================================================
# 2. CUSTOM EXCEPTIONS
# ============================================================================

class WorkflowException(Exception):
    """Base exception for workflow errors"""
    pass

class LLMException(WorkflowException):
    """LLM-related errors"""
    pass

class SearchException(WorkflowException):
    """Search-related errors"""
    pass

class ValidationException(WorkflowException):
    """Data validation errors"""
    pass

# ============================================================================
# 3. INTERFACES & PROTOCOLS
# ============================================================================

class LLMInterface(Protocol):
    """Interface for LLM implementations"""
    
    async def complete(self, prompt: str, **kwargs) -> str:
        """Generate completion for given prompt"""
        ...
    
    async def chat(self, messages: List[Dict[str, str]]) -> str:
        """Generate chat response for given messages"""
        ...

class SearchInterface(Protocol):
    """Interface for search implementations"""
    
    async def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """Search for information using given query"""
        ...

class EventBus(Protocol):
    """Interface for event publishing"""
    
    async def publish(self, event_type: str, data: Any) -> None:
        """Publish an event"""
        ...

# ============================================================================
# 4. DOMAIN MODELS
# ============================================================================

@dataclass
class Section:
    """Domain model for report section"""
    name: str
    description: str
    research: bool
    content: str = ""

@dataclass
class Report:
    """Domain model for complete report"""
    sections: List[Section]
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

# ============================================================================
# 5. SERVICES (BUSINESS LOGIC)
# ============================================================================

class ReportGenerationService:
    """Service containing pure business logic for report generation"""
    
    def __init__(self, llm: LLMInterface, search: SearchInterface):
        self.llm = llm
        self.search = search
    
    async def generate_report_plan(self, query: str) -> Report:
        """Generate report structure based on query"""
        try:
            prompt = self._build_planning_prompt(query)
            response = await self.llm.complete(prompt, response_format={"type": "json_object"})
            return self._parse_report_structure(response)
        except Exception as e:
            raise LLMException(f"Failed to generate report plan: {e}")
    
    async def generate_section_content(self, section: Section) -> str:
        """Generate content for a specific section"""
        if not section.research:
            return section.content
        
        try:
            # Generate search queries
            queries = await self._generate_search_queries(section.description)
            
            # Perform searches
            search_results = await self._perform_searches(queries)
            
            # Generate content using search results
            content = await self._generate_content_from_context(
                section.description, search_results
            )
            
            return content
        except Exception as e:
            raise SearchException(f"Failed to generate section content: {e}")
    
    def _build_planning_prompt(self, query: str) -> str:
        """Build prompt for report planning"""
        return f"""Generate a policy report structure for: {query}
        
        Return JSON with sections containing:
        - name: section name
        - description: section description  
        - research: whether research is needed
        - content: empty string
        """
    
    def _parse_report_structure(self, response: str) -> Report:
        """Parse LLM response into Report object"""
        import json
        try:
            data = json.loads(response)
            sections = [Section(**section) for section in data.get('sections', [])]
            return Report(sections=sections)
        except (json.JSONDecodeError, TypeError) as e:
            raise ValidationException(f"Invalid report structure: {e}")
    
    async def _generate_search_queries(self, topic: str) -> List[str]:
        """Generate search queries for given topic"""
        prompt = f"Generate 3 search queries for policy research on: {topic}"
        response = await self.llm.complete(prompt)
        return [q.strip() for q in response.split('\n') if q.strip()]
    
    async def _perform_searches(self, queries: List[str]) -> str:
        """Perform searches and aggregate results"""
        results = []
        for query in queries:
            try:
                search_result = await self.search.search(query)
                results.append(f"Query: {query}\nResults: {search_result}")
            except Exception as e:
                logging.warning(f"Search failed for query '{query}': {e}")
        
        return "\n\n".join(results)
    
    async def _generate_content_from_context(self, topic: str, context: str) -> str:
        """Generate section content using search context"""
        prompt = f"""Write a policy analysis section on: {topic}
        
        Use this research context:
        {context}
        
        Requirements:
        - 200-300 words
        - Policy-focused
        - Include key insights
        - Cite sources
        """
        
        return await self.llm.complete(prompt)

# ============================================================================
# 6. IMPROVED NODE ARCHITECTURE
# ============================================================================

class BaseWorkflowNode(AsyncNode):
    """Base class for all workflow nodes with common functionality"""
    
    def __init__(self, config: WorkflowConfig, event_bus: Optional[EventBus] = None):
        super().__init__(max_retries=config.max_retries, wait=config.retry_wait)
        self.config = config
        self.event_bus = event_bus
        self.logger = logging.getLogger(self.__class__.__name__)
    
    async def _publish_event(self, event_type: str, data: Any) -> None:
        """Publish event if event bus is available"""
        if self.event_bus:
            await self.event_bus.publish(event_type, data)
    
    async def _handle_error(self, error: Exception, context: str) -> str:
        """Centralized error handling"""
        self.logger.error(f"Error in {context}: {error}")
        await self._publish_event("error", {"context": context, "error": str(error)})
        
        if isinstance(error, LLMException):
            return "llm_error"
        elif isinstance(error, SearchException):
            return "search_error"
        elif isinstance(error, ValidationException):
            return "validation_error"
        else:
            return "system_error"

class IdentifyQuestionNode(BaseWorkflowNode):
    """Improved question identification node"""
    
    def __init__(self, llm: LLMInterface, config: WorkflowConfig, event_bus: Optional[EventBus] = None):
        super().__init__(config, event_bus)
        self.llm = llm
    
    async def prep_async(self, shared: Dict[str, Any]) -> str:
        query = shared.get("query", "").strip()
        await self._publish_event("node_started", {"node": "identify", "query": query})
        return query
    
    async def exec_async(self, query: str) -> str:
        try:
            prompt = f"Classify this query as 'research' or 'chat': {query}"
            response = await self.llm.complete(prompt)
            return response.strip().lower()
        except Exception as e:
            raise LLMException(f"Failed to classify query: {e}")
    
    async def post_async(self, shared: Dict[str, Any], prep_res: str, exec_res: str) -> str:
        if exec_res == "research":
            shared["query_type"] = "research"
            await self._publish_event("query_classified", {"type": "research"})
            return "research"
        else:
            shared["query_type"] = "chat"
            shared["result"] = "This appears to be a chat query, not a research request."
            await self._publish_event("query_classified", {"type": "chat"})
            return "end"

class GenerateReportPlanNode(BaseWorkflowNode):
    """Improved report planning node"""
    
    def __init__(self, service: ReportGenerationService, config: WorkflowConfig, 
                 event_bus: Optional[EventBus] = None):
        super().__init__(config, event_bus)
        self.service = service
    
    async def prep_async(self, shared: Dict[str, Any]) -> str:
        query = shared.get("query", "")
        await self._publish_event("planning_started", {"query": query})
        return query
    
    async def exec_async(self, query: str) -> Report:
        try:
            report = await self.service.generate_report_plan(query)
            return report
        except Exception as e:
            await self._handle_error(e, "report_planning")
            raise
    
    async def post_async(self, shared: Dict[str, Any], prep_res: str, exec_res: Report) -> str:
        if exec_res:
            shared["report"] = exec_res
            await self._publish_event("plan_generated", {
                "sections_count": len(exec_res.sections),
                "sections": [s.name for s in exec_res.sections]
            })
            return "generate_sections"
        else:
            return "end"

# ============================================================================
# 7. FACTORY PATTERN
# ============================================================================

class WorkflowFactory:
    """Factory for creating workflow components"""
    
    def __init__(self, config: WorkflowConfig):
        self.config = config
    
    def create_llm(self) -> LLMInterface:
        """Create LLM instance based on configuration"""
        # This would be implemented with actual LLM classes
        from llama_index.llms.deepseek import DeepSeek
        return DeepSeek(
            api_key=self.config.deepseek_api_key,
            model="deepseek-chat",
            max_tokens=self.config.max_tokens
        )
    
    def create_search_client(self) -> SearchInterface:
        """Create search client based on configuration"""
        # This would be implemented with actual search classes
        pass
    
    def create_workflow(self, event_bus: Optional[EventBus] = None) -> AsyncFlow:
        """Create complete workflow with all nodes"""
        llm = self.create_llm()
        search = self.create_search_client()
        service = ReportGenerationService(llm, search)
        
        # Create nodes
        identify_node = IdentifyQuestionNode(llm, self.config, event_bus)
        plan_node = GenerateReportPlanNode(service, self.config, event_bus)
        
        # Connect nodes
        identify_node - "research" >> plan_node
        
        return AsyncFlow(start=identify_node)

# ============================================================================
# 8. USAGE EXAMPLE
# ============================================================================

async def main():
    """Example usage of improved architecture"""
    
    # Load configuration
    config = WorkflowConfig.from_env()
    
    # Create workflow
    factory = WorkflowFactory(config)
    workflow = factory.create_workflow()
    
    # Run workflow
    shared = {"query": "中国新能源政策分析"}
    await workflow.run_async(shared)
    
    return shared.get("result")

if __name__ == "__main__":
    result = asyncio.run(main())
