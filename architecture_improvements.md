# Policy Decision Workflow - Architectural Improvements

## 🏗️ Current Architecture Issues

### 1. **Monolithic Structure**
- All components in single file (310 lines)
- Hard to maintain and test
- Violates Single Responsibility Principle

### 2. **Tight Coupling**
- Direct LLM implementation dependencies
- Hard-coded client initialization
- No dependency injection

### 3. **Mixed Concerns**
- Business logic + infrastructure code
- Scattered error handling
- No clear layer separation

## 🚀 Recommended Improvements

### 1. **Modular Architecture**

```
src/
├── core/
│   ├── __init__.py
│   ├── config.py              # Centralized configuration
│   ├── exceptions.py          # Custom exception types
│   └── interfaces.py          # Abstract interfaces
├── domain/
│   ├── __init__.py
│   ├── models.py              # Domain models (Report, Section)
│   └── services.py            # Business logic services
├── infrastructure/
│   ├── __init__.py
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── base.py            # LLM interface
│   │   ├── deepseek.py        # DeepSeek implementation
│   │   └── factory.py         # LLM factory
│   ├── search/
│   │   ├── __init__.py
│   │   ├── base.py            # Search interface
│   │   ├── rag_client.py      # RAG implementation
│   │   └── tavily_client.py   # Tavily implementation
│   └── storage/
│       ├── __init__.py
│       └── memory.py          # In-memory storage
├── workflow/
│   ├── __init__.py
│   ├── nodes/
│   │   ├── __init__.py
│   │   ├── base.py            # Base node classes
│   │   ├── identify.py        # IdentifyQuestionNode
│   │   ├── plan.py            # GenerateReportPlanNode
│   │   ├── sections.py        # GenerateSectionsNode
│   │   └── format.py          # FormatReportNode
│   ├── orchestrator.py        # Flow orchestration
│   └── factory.py             # Workflow factory
└── api/
    ├── __init__.py
    └── workflow_api.py        # Public API interface
```

### 2. **Dependency Injection Pattern**

```python
# core/interfaces.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any

class LLMInterface(ABC):
    @abstractmethod
    async def complete(self, prompt: str, **kwargs) -> str:
        pass

class SearchInterface(ABC):
    @abstractmethod
    async def search(self, query: str) -> List[Dict[str, Any]]:
        pass

# workflow/nodes/base.py
from pocketflow import AsyncNode
from core.interfaces import LLMInterface, SearchInterface

class BaseWorkflowNode(AsyncNode):
    def __init__(self, llm: LLMInterface, config: Dict[str, Any]):
        super().__init__(max_retries=config.get('max_retries', 2))
        self.llm = llm
        self.config = config
```

### 3. **Configuration Management**

```python
# core/config.py
from dataclasses import dataclass
from typing import Optional
import os

@dataclass
class WorkflowConfig:
    # LLM Configuration
    deepseek_api_key: str
    max_tokens: int = 2048
    temperature: float = 0.7
    
    # Search Configuration
    tavily_api_key: Optional[str] = None
    query_generation_count: int = 1
    max_search_count: int = 1
    
    # Workflow Configuration
    max_retries: int = 2
    retry_wait: int = 5
    
    @classmethod
    def from_env(cls) -> 'WorkflowConfig':
        return cls(
            deepseek_api_key=os.getenv('DEEPSEEK_API_KEY'),
            tavily_api_key=os.getenv('TAVILY_API_KEY'),
            query_generation_count=int(os.getenv('QUERY_GENERATION_COUNT', 1)),
            max_search_count=int(os.getenv('MAX_SEARCH_COUNT', 1))
        )
```

### 4. **Error Handling Strategy**

```python
# core/exceptions.py
class WorkflowException(Exception):
    """Base exception for workflow errors"""
    pass

class LLMException(WorkflowException):
    """LLM-related errors"""
    pass

class SearchException(WorkflowException):
    """Search-related errors"""
    pass

class ValidationException(WorkflowException):
    """Data validation errors"""
    pass

# workflow/nodes/base.py
class BaseWorkflowNode(AsyncNode):
    async def handle_error(self, error: Exception, context: str) -> str:
        """Centralized error handling"""
        if isinstance(error, LLMException):
            return "llm_error"
        elif isinstance(error, SearchException):
            return "search_error"
        else:
            logger.error(f"Unexpected error in {context}: {error}")
            return "system_error"
```

### 5. **Factory Pattern for Components**

```python
# infrastructure/llm/factory.py
from core.interfaces import LLMInterface
from core.config import WorkflowConfig

class LLMFactory:
    @staticmethod
    def create_llm(config: WorkflowConfig) -> LLMInterface:
        return DeepSeekLLM(
            api_key=config.deepseek_api_key,
            max_tokens=config.max_tokens,
            temperature=config.temperature
        )

# workflow/factory.py
class WorkflowFactory:
    def __init__(self, config: WorkflowConfig):
        self.config = config
        self.llm = LLMFactory.create_llm(config)
        self.search_client = SearchFactory.create_search_client(config)
    
    def create_workflow(self) -> AsyncFlow:
        nodes = self._create_nodes()
        return self._connect_nodes(nodes)
```

### 6. **Service Layer for Business Logic**

```python
# domain/services.py
class ReportGenerationService:
    def __init__(self, llm: LLMInterface, search: SearchInterface):
        self.llm = llm
        self.search = search
    
    async def generate_report_plan(self, query: str) -> Report:
        """Pure business logic for report planning"""
        pass
    
    async def generate_section_content(self, section: Section) -> str:
        """Pure business logic for content generation"""
        pass
```

### 7. **Async Context Managers for Resource Management**

```python
# infrastructure/llm/base.py
class BaseLLM:
    async def __aenter__(self):
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()

# Usage in nodes
async def exec_async(self, prep_res):
    async with self.llm as llm:
        response = await llm.complete(prompt)
        return response
```

### 8. **Event-Driven Progress Tracking**

```python
# core/events.py
from dataclasses import dataclass
from typing import Any

@dataclass
class WorkflowEvent:
    event_type: str
    node_name: str
    data: Any
    timestamp: float

class EventBus:
    def __init__(self):
        self.subscribers = []
    
    async def publish(self, event: WorkflowEvent):
        for subscriber in self.subscribers:
            await subscriber.handle_event(event)
```

### 9. **Testing Architecture**

```python
# tests/conftest.py
@pytest.fixture
def mock_llm():
    return MockLLM()

@pytest.fixture
def workflow_config():
    return WorkflowConfig(
        deepseek_api_key="test-key",
        max_retries=1
    )

@pytest.fixture
def workflow_factory(mock_llm, workflow_config):
    factory = WorkflowFactory(workflow_config)
    factory.llm = mock_llm
    return factory
```

### 10. **Performance Monitoring**

```python
# core/monitoring.py
import time
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"{func.__name__} completed in {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"{func.__name__} failed after {duration:.2f}s: {e}")
            raise
    return wrapper
```

## 🎯 Implementation Priority

1. **Phase 1**: Extract configuration and interfaces
2. **Phase 2**: Separate nodes into individual files
3. **Phase 3**: Implement dependency injection
4. **Phase 4**: Add proper error handling
5. **Phase 5**: Add monitoring and events
6. **Phase 6**: Comprehensive testing

## 📈 Benefits

- **Maintainability**: Easier to modify and extend
- **Testability**: Each component can be tested in isolation
- **Scalability**: Easy to add new nodes or modify existing ones
- **Reliability**: Better error handling and monitoring
- **Performance**: Optimized resource management
- **Developer Experience**: Clear structure and documentation
