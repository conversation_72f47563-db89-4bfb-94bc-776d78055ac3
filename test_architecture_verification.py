#!/usr/bin/env python3
"""
Real test to verify the improved architecture works with actual API calls.
This test validates the complete workflow end-to-end with real LLM and search services.
"""

import sys
import os
import asyncio
import time
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import existing working modules
from llama_index.llms.deepseek import DeepSeek
from tavily import AsyncTavilyClient

# Import our improved architecture
try:
    from src.core.config import WorkflowConfig
    from src.api.workflow_api import PolicyDecisionWorkflowAPI
    from src.domain.models import WorkflowState, QueryType, Report, Section
    from src.core.exceptions import WorkflowException, LLMException
    ARCHITECTURE_AVAILABLE = True
    print("✅ Improved architecture modules imported successfully")
except ImportError as e:
    ARCHITECTURE_AVAILABLE = False
    print(f"❌ Architecture import failed: {e}")

# Import original workflow for comparison
from policy_decision_workflow_pocketflow import run_policy_decision_workflow, create_policy_decision_flow


class ArchitectureVerificationTest:
    """Comprehensive test suite for the improved architecture"""
    
    def __init__(self):
        self.deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
        self.tavily_api_key = os.getenv('TAVILY_API_KEY')
        self.test_query = "中国新能源汽车政策的主要特点"
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    def check_environment(self):
        """Check if required environment variables are set"""
        print("=== Environment Check ===")
        
        if not self.deepseek_api_key:
            print("❌ DEEPSEEK_API_KEY not found")
            return False
        else:
            print(f"✅ DEEPSEEK_API_KEY: {self.deepseek_api_key[:10]}...")
            
        if not self.tavily_api_key:
            print("⚠️  TAVILY_API_KEY not found (optional)")
        else:
            print(f"✅ TAVILY_API_KEY: {self.tavily_api_key[:10]}...")
            
        return True
    
    def test_architecture_structure(self):
        """Test if the improved architecture structure exists"""
        print("\n=== Testing Architecture Structure ===")
        
        try:
            # Check if src directory exists
            if not os.path.exists('src'):
                self.log_test_result("Architecture Structure", False, "src/ directory not found")
                return False
            
            # Check core components
            core_files = ['src/core/config.py', 'src/core/exceptions.py', 'src/core/interfaces.py']
            missing_files = []
            for file in core_files:
                if not os.path.exists(file):
                    missing_files.append(file)
            
            if missing_files:
                self.log_test_result("Core Components", False, f"Missing: {missing_files}")
            else:
                self.log_test_result("Core Components", True, "All core files present")
            
            # Check domain components
            domain_files = ['src/domain/models.py', 'src/domain/services.py']
            missing_domain = []
            for file in domain_files:
                if not os.path.exists(file):
                    missing_domain.append(file)
            
            if missing_domain:
                self.log_test_result("Domain Components", False, f"Missing: {missing_domain}")
            else:
                self.log_test_result("Domain Components", True, "All domain files present")
            
            # Check API
            if os.path.exists('src/api/workflow_api.py'):
                self.log_test_result("API Component", True, "API file present")
            else:
                self.log_test_result("API Component", False, "API file missing")
            
            return len(missing_files) == 0 and len(missing_domain) == 0
            
        except Exception as e:
            self.log_test_result("Architecture Structure", False, str(e))
            return False
    
    def test_architecture_imports(self):
        """Test if we can import the new architecture components"""
        print("\n=== Testing Architecture Imports ===")
        
        if not ARCHITECTURE_AVAILABLE:
            self.log_test_result("Architecture Imports", False, "Import failed during initialization")
            return False
        
        try:
            # Test configuration creation
            config = WorkflowConfig(deepseek_api_key="test-key")
            self.log_test_result("Configuration Creation", True, "WorkflowConfig created successfully")
            
            # Test validation
            try:
                invalid_config = WorkflowConfig(deepseek_api_key="")
                self.log_test_result("Configuration Validation", False, "Should have failed with empty API key")
                return False
            except ValueError:
                self.log_test_result("Configuration Validation", True, "Correctly rejected empty API key")
            
            # Test domain models
            state = WorkflowState(query="test")
            state.set_query_type(QueryType.RESEARCH)
            self.log_test_result("Domain Models", True, "WorkflowState and QueryType work correctly")
            
            # Test exceptions
            try:
                raise LLMException("Test exception")
            except WorkflowException:
                self.log_test_result("Exception Hierarchy", True, "Exception hierarchy works correctly")
            
            return True
            
        except Exception as e:
            self.log_test_result("Architecture Imports", False, str(e))
            return False
    
    async def test_original_workflow(self):
        """Test the original workflow"""
        print("\n=== Testing Original Workflow ===")
        
        try:
            # Initialize LLM with model parameter
            llm = DeepSeek(api_key=self.deepseek_api_key, model="deepseek-chat")
            
            # Run original workflow
            start_time = time.time()
            result = await run_policy_decision_workflow(
                query=self.test_query,
                llm=llm,
                tavily_api_key=self.tavily_api_key,
                verbose=True
            )
            execution_time = time.time() - start_time
            
            # Analyze results
            success = result and 'result' in result
            details = f"Completed in {execution_time:.2f}s, Result length: {len(str(result.get('result', '')))}"
            self.log_test_result("Original Workflow Execution", success, details)
            
            if success:
                progress_count = len(result.get('progress_messages', []))
                self.log_test_result("Original Progress Tracking", progress_count > 0, f"{progress_count} progress messages")
                
                has_chart_data = bool(result.get('chart_data'))
                self.log_test_result("Original Chart Generation", has_chart_data, f"Chart data: {has_chart_data}")
            
            return success, result, execution_time
            
        except Exception as e:
            self.log_test_result("Original Workflow Execution", False, str(e))
            return False, None, 0
    
    async def test_improved_workflow(self):
        """Test the improved workflow if available"""
        print("\n=== Testing Improved Workflow ===")
        
        if not ARCHITECTURE_AVAILABLE:
            self.log_test_result("Improved Workflow", False, "Architecture not available")
            return False, None, 0
        
        try:
            # Create configuration
            config = WorkflowConfig(
                deepseek_api_key=self.deepseek_api_key,
                tavily_api_key=self.tavily_api_key,
                max_retries=2,
                query_generation_count=1,
                max_search_count=1
            )
            
            # Create API instance
            api = PolicyDecisionWorkflowAPI(config)
            
            # Run improved workflow
            start_time = time.time()
            result = await api.run_workflow(
                query=self.test_query,
                verbose=True
            )
            execution_time = time.time() - start_time
            
            # Analyze results
            success = result and result.get('success', False)
            details = f"Completed in {execution_time:.2f}s, Success: {success}"
            self.log_test_result("Improved Workflow Execution", success, details)
            
            if result:
                has_query_type = bool(result.get('query_type'))
                self.log_test_result("Query Type Classification", has_query_type, f"Query type: {result.get('query_type', 'N/A')}")
                
                progress_count = len(result.get('progress_messages', []))
                self.log_test_result("Enhanced Progress Tracking", progress_count > 0, f"{progress_count} progress messages")
                
                if result.get('result'):
                    result_length = len(str(result['result']))
                    self.log_test_result("Content Generation", result_length > 100, f"Result length: {result_length}")
            
            return success, result, execution_time
            
        except Exception as e:
            self.log_test_result("Improved Workflow Execution", False, str(e))
            return False, None, 0
    
    def compare_workflows(self, original_result, improved_result, original_time, improved_time):
        """Compare results from both workflows"""
        print("\n=== Workflow Comparison ===")
        
        if original_result and improved_result:
            print("📊 Both workflows completed successfully!")
            print(f"   Original time: {original_time:.2f}s")
            print(f"   Improved time: {improved_time:.2f}s")
            
            # Performance comparison
            if improved_time < original_time:
                improvement = ((original_time - improved_time) / original_time) * 100
                print(f"   🚀 Improved workflow is {improvement:.1f}% faster")
            else:
                slowdown = ((improved_time - original_time) / original_time) * 100
                print(f"   ⚠️  Improved workflow is {slowdown:.1f}% slower (expected due to additional features)")
            
            # Feature comparison
            improved_features = []
            if improved_result.get('query_type'):
                improved_features.append("Query type classification")
            if improved_result.get('success') is not None:
                improved_features.append("Success status tracking")
            if 'metadata' in improved_result:
                improved_features.append("Metadata tracking")
            
            if improved_features:
                print(f"   ✨ Additional features in improved version:")
                for feature in improved_features:
                    print(f"      - {feature}")
        
        elif original_result and not improved_result:
            print("⚠️  Only original workflow completed")
        elif not original_result and improved_result:
            print("🎉 Only improved workflow completed (architecture is more robust!)")
        else:
            print("❌ Both workflows failed")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*70)
        print("🧪 ARCHITECTURE VERIFICATION TEST SUMMARY")
        print("="*70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['details']}")
        
        print("\n" + "="*70)
        if failed_tests == 0:
            print("🎉 ALL TESTS PASSED! The improved architecture is working perfectly!")
        elif passed_tests > failed_tests:
            print("✅ MOSTLY SUCCESSFUL! The architecture is working with minor issues.")
        else:
            print("⚠️  SOME ISSUES FOUND. Please review the failed tests above.")
        
        return failed_tests == 0


async def main():
    """Run the complete architecture verification test"""
    print("🏗️  Policy Decision Workflow - Architecture Verification Test")
    print("="*70)
    print("This test verifies the improved architecture with real API calls.")
    print("="*70)
    
    # Initialize test suite
    test_suite = ArchitectureVerificationTest()
    
    # Check environment
    if not test_suite.check_environment():
        print("❌ Environment check failed. Please set DEEPSEEK_API_KEY.")
        return False
    
    # Test architecture structure
    structure_ok = test_suite.test_architecture_structure()
    
    # Test architecture imports
    imports_ok = test_suite.test_architecture_imports()
    
    # Test workflows
    print(f"\n🧪 Testing with query: '{test_suite.test_query}'")
    original_success, original_result, original_time = await test_suite.test_original_workflow()
    improved_success, improved_result, improved_time = await test_suite.test_improved_workflow()
    
    # Compare workflows
    test_suite.compare_workflows(original_result, improved_result, original_time, improved_time)
    
    # Print summary
    overall_success = test_suite.print_summary()
    
    if overall_success:
        print("\n🚀 ARCHITECTURE BENEFITS ACHIEVED:")
        print("  🔧 Modular, maintainable code structure")
        print("  ⚙️  Centralized configuration management")
        print("  🚨 Structured exception handling")
        print("  🏢 Rich domain models with business logic")
        print("  🧪 Improved testability and debugging")
        print("  📊 Enhanced progress tracking and observability")
        print("  🛡️  Better error handling and recovery")
        print("  🚀 Scalable design for future enhancements")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
