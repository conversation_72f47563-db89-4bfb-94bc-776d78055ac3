"""
Improved Policy Decision Workflow using the new modular architecture.

This file provides a clean interface that uses the new architectural components
while maintaining backward compatibility with the original API.
"""

import sys
import os
import asyncio
import logging
from typing import Dict, Any, Optional

# Add src to Python path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.config import WorkflowConfig, NodeConfig
from src.core.exceptions import WorkflowException, ConfigurationException
from src.api.workflow_api import PolicyDecisionWorkflowAPI, run_policy_decision_workflow, create_policy_decision_flow

# Re-export the main API functions for backward compatibility
__all__ = [
    'run_policy_decision_workflow',
    'create_policy_decision_flow',
    'PolicyDecisionWorkflowAPI',
    'WorkflowConfig',
    'NodeConfig'
]

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


async def main():
    """
    Example usage of the improved workflow.
    """
    try:
        # Example 1: Using the high-level API
        print("=== Example 1: High-level API ===")
        
        result = await run_policy_decision_workflow(
            query="中国新能源汽车政策的发展趋势",
            verbose=True
        )
        
        print(f"Query: {result['query']}")
        print(f"Query Type: {result['query_type']}")
        print(f"Success: {result['success']}")
        if result.get('result'):
            print(f"Result length: {len(result['result'])} characters")
        
        print("\n" + "="*50 + "\n")
        
        # Example 2: Using custom configuration
        print("=== Example 2: Custom Configuration ===")
        
        config = WorkflowConfig(
            deepseek_api_key=os.getenv('DEEPSEEK_API_KEY', ''),
            max_retries=1,
            query_generation_count=2,
            max_search_count=2
        )
        
        api = PolicyDecisionWorkflowAPI(config)
        result2 = await api.run_workflow(
            query="碳中和政策实施策略分析",
            verbose=True
        )
        
        print(f"Query: {result2['query']}")
        print(f"Success: {result2['success']}")
        print(f"Progress messages: {len(result2['progress_messages'])}")
        
        print("\n" + "="*50 + "\n")
        
        # Example 3: Using the flow directly
        print("=== Example 3: Direct Flow Usage ===")
        
        flow = create_policy_decision_flow(config=config)
        
        shared_state = {
            "query": "智能交通政策建议",
            "progress_messages": []
        }
        
        await flow.run_async(shared_state)
        
        print(f"Query: {shared_state['query']}")
        print(f"Final result available: {'result' in shared_state}")
        
    except ConfigurationException as e:
        print(f"Configuration error: {e}")
        print("Please check your environment variables (DEEPSEEK_API_KEY)")
    except WorkflowException as e:
        print(f"Workflow error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
