#!/usr/bin/env python3

import sys
import os

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from models import Report, Section

def test_section_creation():
    """Test that Section can be created with correct fields"""
    try:
        # Test creating Section with correct fields
        section = Section(
            name="引言",
            description="中国新能源政策概述", 
            research=True,
            content="这是引言内容"
        )
        print("✓ Section created successfully with correct fields")
        print(f"  name: {section.name}")
        print(f"  description: {section.description}")
        print(f"  research: {section.research}")
        print(f"  content: {section.content}")
        
        # Test creating Report with sections
        sections = [
            Section(name="引言", description="描述", research=True, content="引言内容"),
            Section(name="主体", description="描述", research=True, content="主体内容"),
            Section(name="结论", description="描述", research=True, content="结论内容")
        ]
        report = Report(sections=sections)
        print("✓ Report created successfully with sections")
        print(f"  Number of sections: {len(report.sections)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error creating Section: {e}")
        return False

def test_section_with_wrong_fields():
    """Test that Section fails with wrong field names"""
    try:
        # This should fail - using 'title' instead of 'name'
        section = Section(
            title="引言",  # Wrong field name
            description="中国新能源政策概述",
            research=True,
            content="这是引言内容"
        )
        print("✗ Section should have failed with wrong field name")
        return False
        
    except Exception as e:
        print(f"✓ Section correctly failed with wrong field name: {type(e).__name__}")
        return True

if __name__ == "__main__":
    print("Testing Section model validation...")
    print("=" * 50)
    
    success1 = test_section_creation()
    print()
    success2 = test_section_with_wrong_fields()
    
    print()
    print("=" * 50)
    if success1 and success2:
        print("✓ All tests passed!")
        sys.exit(0)
    else:
        print("✗ Some tests failed!")
        sys.exit(1)
