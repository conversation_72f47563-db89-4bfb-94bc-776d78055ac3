from pocketflow import Node, AsyncNode, AsyncFlow
import asyncio
import os
import json
from dotenv import load_dotenv
from tavily import AsyncTavilyClient
from llama_index.llms.deepseek import DeepSeek
from llama_index.core.llms import ChatMessage
import prompts
from models import Section, Report
from rag_client import RAGClient

# 加载环境变量
load_dotenv()

QUERY_GENERATION_COUNT = int(os.environ.get("QUERY_GENERATION_COUNT", 1))
MAX_SEARCH_COUNT = int(os.environ.get("MAX_SEARCH_COUNT", 1))
SERVICE_UNAVAILABLE = "服务当前不可用,请稍后再试..."

class IdentifyQuestionNode(Node):
    def __init__(self, llm, thinking_llm):
        super().__init__(max_retries=2, wait=5)
        self.llm = llm
        self.thinking_llm = thinking_llm
        self.messages = [
            ChatMessage(role="system", content=prompts.agent_prompt)
        ]
    
    def prep(self, shared):
        query = shared.get("query", "").strip()
        self.save_messages(self.messages, ChatMessage(role="user", content=query))
        return self.messages
    
    def exec(self, messages):
        try:
            chat_response = self.thinking_llm.chat(messages)
            response = chat_response.message.content and chat_response.message.content.strip()
            print(response)
            return response
        except Exception as e:
            print(f"Error identifying query type: {e}")
            return SERVICE_UNAVAILABLE
    
    def post(self, shared, prep_res, exec_res):
        if exec_res == SERVICE_UNAVAILABLE:
            shared["result"] = SERVICE_UNAVAILABLE
            return "end"
            
        if exec_res == "research":
            print("type:", "research")
            shared["query_type"] = "research"
            return "research"
        else:
            print("type:", "chat")
            self.save_messages(self.messages, ChatMessage(role="assistant", content=exec_res))
            shared["result"] = exec_res
            return "end"
    
    def save_messages(self, messages, message):
        messages.append(message)

class GenerateReportPlanNode(AsyncNode):
    def __init__(self, llm, rag_client):
        super().__init__(max_retries=2, wait=5)
        self.llm = llm
        self.rag_client = rag_client
    
    async def prep_async(self, shared):
        query = shared.get("query", "")
        if self.rag_client:
            await self.rag_client.find_collection(topic=query)
        
        shared["progress_messages"] = shared.get("progress_messages", [])
        shared["progress_messages"].append("### 开始生成报告计划 \n")
        
        prompt = prompts.report_planner_instructions.format(topic=query)
        return prompt
    
    async def exec_async(self, prompt):
        try:
            response = self.llm.complete(prompt, response_format={"type": "json_object"})
            parsed_data = json.loads(response.text)
            return parsed_data
        except Exception as e:
            print(f"Error generating report plan: {e}")
            return None
    
    async def post_async(self, shared, prep_res, exec_res):
        if not exec_res:
            shared["result"] = SERVICE_UNAVAILABLE
            return "end"
        
        try:
            report_data = exec_res["report"]
            all_data = []
            all_data.append(report_data["introduction"])
            all_data.extend(report_data["mainBodySections"])
            all_data.append(report_data["conclusion"])
            
            report = Report(sections=[Section(**section) for section in all_data])
            shared["report"] = report
            
            sections_contents = "\n - ".join([s["description"] for s in all_data])
            shared["progress_messages"].append("\n" + sections_contents)
            
            return "generate_sections"
        except Exception as e:
            print(f"Error processing report plan: {e}")
            shared["result"] = SERVICE_UNAVAILABLE
            return "end"

class GenerateSectionsNode(AsyncNode):
    def __init__(self, llm, rag_client, tavily_client=None):
        super().__init__(max_retries=2, wait=5)
        self.llm = llm
        self.rag_client = rag_client
        self.tavily_client = tavily_client
    
    async def prep_async(self, shared):
        shared["progress_messages"].append("\n ### 开始生成报告章节 \n")
        return shared.get("report")
    
    async def exec_async(self, report):
        if not report:
            return None
            
        try:
            updated_sections = []
            
            for section in report.sections:
                if section.research and not section.content:
                    # 生成查询
                    queries = await self.generate_queries(section.description, QUERY_GENERATION_COUNT)
                    # 执行搜索
                    results = await self.perform_searches(queries, tavily_search_criteria={"search_depth": "basic", "max_results": MAX_SEARCH_COUNT})
                    # 生成内容
                    prompt = prompts.section_writer_instructions.format(section_topic=section.description, context=results)
                    response = self.llm.complete(prompt)
                    section.content = response.text
                
                updated_sections.append(section)
            
            report.sections = updated_sections
            return report
        except Exception as e:
            print(f"Error generating sections: {e}")
            return None
    
    async def post_async(self, shared, prep_res, exec_res):
        if not exec_res:
            shared["result"] = SERVICE_UNAVAILABLE
            return "end"
        
        shared["report"] = exec_res
        shared["progress_messages"].append("### 所有章节生成完成 \n\n")
        
        return "format_report"
    
    async def generate_queries(self, topic, count=1):
        # 实现查询生成逻辑
        prompt = prompts.query_generator_instructions.format(topic=topic, count=count)
        try:
            response = self.llm.complete(prompt, response_format={"type": "json_object"})
            parsed_data = json.loads(response.text)
            return parsed_data.get("queries", [topic])
        except Exception as e:
            print(f"Error generating queries: {e}")
            return [topic]
    
    async def perform_searches(self, queries, tavily_search_criteria=None):
        # 实现搜索逻辑
        search_results = []
        
        # RAG搜索
        if self.rag_client:
            try:
                for query in queries:
                    rag_results = await self.rag_client.search_knowledge_base(query)
                    if rag_results:
                        search_results.append(f"RAG结果 - 查询: {query}\n{rag_results}")
            except Exception as e:
                print(f"Error in RAG search: {e}")
        
        # Tavily搜索
        if self.tavily_client and tavily_search_criteria:
            try:
                for query in queries:
                    tavily_results = await self.tavily_client.search(query=query, **tavily_search_criteria)
                    if tavily_results:
                        formatted_results = "\n".join([f"- {result['title']}: {result['content']}" for result in tavily_results["results"]])
                        search_results.append(f"Web搜索 - 查询: {query}\n{formatted_results}")
            except Exception as e:
                print(f"Error in Tavily search: {e}")
        
        return "\n\n".join(search_results)

class FormatReportNode(Node):
    def __init__(self, llm):
        super().__init__(max_retries=2, wait=5)
        self.llm = llm
    
    def prep(self, shared):
        shared["progress_messages"].append("### 格式化最终报告 \n")
        return shared.get("report")
    
    def exec(self, report):
        if not report:
            return None
            
        try:
            # 格式化报告
            formatted_report = ""
            
            # 添加标题
            formatted_report += f"# 研究报告\n\n"
            
            # 添加各部分内容
            for section in report.sections:
                formatted_report += f"## {section.name}\n\n"
                formatted_report += f"{section.content}\n\n"
            
            # 生成图表数据
            chart_data = self.generate_chart_data(report)
            
            return {
                "formatted_report": formatted_report,
                "chart_data": chart_data
            }
        except Exception as e:
            print(f"Error formatting report: {e}")
            return None
    
    def post(self, shared, prep_res, exec_res):
        if not exec_res:
            shared["result"] = SERVICE_UNAVAILABLE
            return "end"
        
        shared["formatted_report"] = exec_res["formatted_report"]
        shared["chart_data"] = exec_res["chart_data"]
        shared["result"] = exec_res["formatted_report"]
        shared["progress_messages"].append("### 报告生成完成! \n")
        
        return "end"
    
    def generate_chart_data(self, report):
        # 实现图表数据生成逻辑
        try:
            # 提取报告内容
            report_content = "\n".join([section.content for section in report.sections if section.content])
            
            # 生成图表数据的提示
            prompt = prompts.chart_data_generator_instructions.format(report_content=report_content)
            
            # 调用LLM生成图表数据
            response = self.llm.complete(prompt, response_format={"type": "json_object"})
            chart_data = json.loads(response.text)
            
            return chart_data
        except Exception as e:
            print(f"Error generating chart data: {e}")
            return {}

def create_policy_decision_flow(llm, tavily_api_key=None, verbose=False):
    # 初始化模型和客户端
    thinking_llm = DeepSeek(model="deepseek-reasoner", api_key=os.environ.get("DEEPSEEK_API_KEY"))
    tavily_client = AsyncTavilyClient(api_key=tavily_api_key) if tavily_api_key else None
    
    try:
        rag_client = RAGClient(llm=llm)
        print("RAG client initialized successfully")
    except Exception as e:
        print(f"Error initializing RAG client: {e}")
        rag_client = None
    
    # 创建节点
    identify_node = IdentifyQuestionNode(llm, thinking_llm)
    plan_node = GenerateReportPlanNode(llm, rag_client)
    sections_node = GenerateSectionsNode(llm, rag_client, tavily_client)
    format_node = FormatReportNode(llm)
    
    # 连接节点
    identify_node - "research" >> plan_node
    plan_node - "generate_sections" >> sections_node
    sections_node - "format_report" >> format_node
    
    # 创建流程
    flow = AsyncFlow(start=identify_node)
    return flow

async def run_policy_decision_workflow(query, llm, tavily_api_key=None, verbose=False):
    # 创建流程
    flow = create_policy_decision_flow(llm, tavily_api_key, verbose)

    # 初始化共享状态
    shared = {
        "query": query,
        "progress_messages": []
    }

    # 运行流程
    await flow.run_async(shared)
    
    # 返回结果
    return {
        "result": shared.get("result", SERVICE_UNAVAILABLE),
        "progress_messages": shared.get("progress_messages", []),
        "chart_data": shared.get("chart_data", {})
    }
    
