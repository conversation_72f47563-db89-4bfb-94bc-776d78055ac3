#!/usr/bin/env python3
"""
Simple test to verify the architecture improvements work.
"""

import asyncio
import os
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Test original workflow
from llama_index.llms.deepseek import DeepSeek
from policy_decision_workflow_pocketflow import run_policy_decision_workflow

async def test_original_workflow():
    """Test the original workflow"""
    print("🧪 Testing Original Workflow")
    print("="*40)
    
    # Check API key
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        print("❌ DEEPSEEK_API_KEY not found")
        return False
    
    print(f"✅ API Key: {api_key[:10]}...")
    
    try:
        # Initialize LLM
        llm = DeepSeek(api_key=api_key)
        print("✅ LLM initialized")
        
        # Test query
        test_query = "中国新能源汽车政策的主要特点"
        print(f"📝 Query: {test_query}")
        
        # Run workflow
        start_time = time.time()
        result = await run_policy_decision_workflow(
            query=test_query,
            llm=llm,
            tavily_api_key=os.getenv('TAVILY_API_KEY'),
            verbose=True
        )
        execution_time = time.time() - start_time
        
        # Analyze results
        print(f"\n📊 Results:")
        print(f"   Execution time: {execution_time:.2f}s")
        print(f"   Result type: {type(result)}")
        print(f"   Has result: {'result' in result}")
        print(f"   Progress messages: {len(result.get('progress_messages', []))}")
        
        if result.get('result'):
            result_text = str(result['result'])
            print(f"   Result length: {len(result_text)} characters")
            if len(result_text) > 100:
                print(f"   Preview: {result_text[:100]}...")
        
        if result.get('chart_data'):
            print(f"   Chart data: {len(result['chart_data'])} items")
        
        print("\n✅ Original workflow test PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Original workflow test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_architecture_structure():
    """Test if the improved architecture structure exists"""
    print("\n🏗️  Testing Architecture Structure")
    print("="*40)
    
    # Check if src directory exists
    if os.path.exists('src'):
        print("✅ src/ directory exists")
        
        # Check core components
        core_files = ['src/core/config.py', 'src/core/exceptions.py', 'src/core/interfaces.py']
        for file in core_files:
            if os.path.exists(file):
                print(f"✅ {file} exists")
            else:
                print(f"❌ {file} missing")
        
        # Check domain components
        domain_files = ['src/domain/models.py', 'src/domain/services.py']
        for file in domain_files:
            if os.path.exists(file):
                print(f"✅ {file} exists")
            else:
                print(f"❌ {file} missing")
        
        # Check infrastructure
        infra_dirs = ['src/infrastructure/llm', 'src/infrastructure/search']
        for dir in infra_dirs:
            if os.path.exists(dir):
                print(f"✅ {dir}/ exists")
            else:
                print(f"❌ {dir}/ missing")
        
        # Check workflow components
        workflow_files = ['src/workflow/factory.py', 'src/workflow/nodes']
        for file in workflow_files:
            if os.path.exists(file):
                print(f"✅ {file} exists")
            else:
                print(f"❌ {file} missing")
        
        # Check API
        if os.path.exists('src/api/workflow_api.py'):
            print("✅ src/api/workflow_api.py exists")
        else:
            print("❌ src/api/workflow_api.py missing")
        
        print("\n✅ Architecture structure test PASSED!")
        return True
    else:
        print("❌ src/ directory not found")
        return False

def test_imports():
    """Test if we can import the new architecture components"""
    print("\n📦 Testing Architecture Imports")
    print("="*40)
    
    try:
        # Test core imports
        import sys
        sys.path.append('src')
        
        from src.core.config import WorkflowConfig
        print("✅ WorkflowConfig imported")
        
        from src.core.exceptions import WorkflowException
        print("✅ WorkflowException imported")
        
        from src.domain.models import WorkflowState, QueryType
        print("✅ Domain models imported")
        
        # Test configuration creation
        config = WorkflowConfig(deepseek_api_key="test-key")
        print("✅ Configuration creation works")
        
        # Test domain model
        state = WorkflowState(query="test")
        state.set_query_type(QueryType.RESEARCH)
        print("✅ Domain model works")
        
        print("\n✅ Import test PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import test FAILED: {e}")
        return False

async def main():
    """Run all tests"""
    print("🏗️  Policy Decision Workflow - Simple Architecture Test")
    print("="*60)
    
    # Test architecture structure
    structure_ok = test_architecture_structure()
    
    # Test imports
    imports_ok = test_imports()
    
    # Test original workflow
    workflow_ok = await test_original_workflow()
    
    # Summary
    print("\n" + "="*60)
    print("🎯 TEST SUMMARY")
    print("="*60)
    
    print(f"✅ Architecture structure: {'PASS' if structure_ok else 'FAIL'}")
    print(f"✅ Architecture imports: {'PASS' if imports_ok else 'FAIL'}")
    print(f"✅ Original workflow: {'PASS' if workflow_ok else 'FAIL'}")
    
    overall_success = structure_ok and imports_ok and workflow_ok
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("The improved architecture is successfully implemented and working!")
        print("\n🚀 Key Benefits Achieved:")
        print("  📁 Modular file structure")
        print("  ⚙️  Centralized configuration")
        print("  🚨 Custom exception handling")
        print("  🏢 Rich domain models")
        print("  🔧 Better maintainability")
        print("  🧪 Improved testability")
    else:
        print("\n⚠️  SOME TESTS FAILED")
        print("Please check the issues above.")
    
    return overall_success

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
