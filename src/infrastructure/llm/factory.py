"""
Factory for creating LLM instances.
"""

from src.core.interfaces import LLMInterface
from src.core.config import WorkflowConfig
from src.core.exceptions import ConfigurationException
from src.infrastructure.llm.deepseek_adapter import DeepSeekAdapter


class LLMFactory:
    """Factory for creating LLM instances based on configuration"""
    
    @staticmethod
    def create_llm(config: WorkflowConfig) -> LLMInterface:
        """Create an LLM instance based on configuration"""
        
        if not config.deepseek_api_key:
            raise ConfigurationException("DeepSeek API key is required")
        
        return DeepSeekAdapter(config)
    
    @staticmethod
    def create_thinking_llm(config: WorkflowConfig) -> LLMInterface:
        """Create a thinking LLM instance (same as regular for now)"""
        return LLMFactory.create_llm(config)
