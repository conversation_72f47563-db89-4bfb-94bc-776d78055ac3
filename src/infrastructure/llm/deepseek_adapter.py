"""
DeepSeek LLM adapter implementing the LLMInterface.
"""

import logging
from typing import List, Dict, Any, Optional

from llama_index.llms.deepseek import DeepSeek
from src.core.interfaces import LLMInterface, LLMResponse
from src.core.exceptions import LLMException
from src.core.config import WorkflowConfig


logger = logging.getLogger(__name__)


class DeepSeekAdapter(LLMInterface):
    """Adapter for DeepSeek LLM that implements LLMInterface"""
    
    def __init__(self, config: WorkflowConfig):
        self.config = config
        self._llm = None
        self._thinking_llm = None
    
    def _get_llm(self) -> DeepSeek:
        """Get or create the main LLM instance"""
        if self._llm is None:
            self._llm = DeepSeek(
                api_key=self.config.deepseek_api_key,
                model=self.config.deepseek_model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
        return self._llm
    
    def _get_thinking_llm(self) -> DeepSeek:
        """Get or create the thinking LLM instance"""
        if self._thinking_llm is None:
            self._thinking_llm = DeepSeek(
                api_key=self.config.deepseek_api_key,
                model=self.config.deepseek_reasoner_model,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
        return self._thinking_llm
    
    async def complete(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate completion for the given prompt"""
        try:
            llm = self._get_llm()
            
            # Handle response format for JSON requests
            if kwargs.get('response_format', {}).get('type') == 'json_object':
                # Add JSON instruction to prompt
                prompt = f"{prompt}\n\nPlease respond with valid JSON only."
            
            response = await llm.acomplete(prompt)
            
            return LLMResponse(
                text=str(response),
                metadata={
                    "model": self.config.deepseek_model,
                    "prompt_length": len(prompt),
                    "response_length": len(str(response))
                }
            )
            
        except Exception as e:
            logger.error(f"DeepSeek completion failed: {e}")
            raise LLMException(f"LLM completion failed: {e}")
    
    async def chat(self, messages: List[Dict[str, str]]) -> LLMResponse:
        """Generate chat response for the given messages"""
        try:
            llm = self._get_thinking_llm()  # Use thinking model for chat
            
            response = await llm.achat(messages)
            
            return LLMResponse(
                text=str(response),
                metadata={
                    "model": self.config.deepseek_reasoner_model,
                    "message_count": len(messages),
                    "response_length": len(str(response))
                }
            )
            
        except Exception as e:
            logger.error(f"DeepSeek chat failed: {e}")
            raise LLMException(f"LLM chat failed: {e}")
    
    async def __aenter__(self) -> 'DeepSeekAdapter':
        """Async context manager entry"""
        # Initialize connections if needed
        logger.debug("DeepSeek adapter context entered")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit"""
        # Cleanup resources if needed
        logger.debug("DeepSeek adapter context exited")
        pass
