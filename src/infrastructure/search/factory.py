"""
Factory for creating search client instances.
"""

from typing import Optional

from src.core.interfaces import SearchInterface
from src.core.config import WorkflowConfig
from src.infrastructure.search.tavily_adapter import <PERSON><PERSON><PERSON><PERSON><PERSON>er, MockSearchAdapter


class SearchFactory:
    """Factory for creating search client instances"""
    
    @staticmethod
    def create_search_client(config: WorkflowConfig) -> Optional[SearchInterface]:
        """Create a search client based on configuration"""
        
        if config.tavily_api_key:
            return TavilyAdapter(config)
        else:
            # Return mock adapter for testing/development
            return MockSearchAdapter(config)
    
    @staticmethod
    def create_mock_search_client(config: WorkflowConfig) -> SearchInterface:
        """Create a mock search client for testing"""
        return MockSearchAdapter(config)
