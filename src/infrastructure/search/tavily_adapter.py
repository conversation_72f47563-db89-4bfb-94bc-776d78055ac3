"""
Tavily search adapter implementing the SearchInterface.
"""

import logging
from typing import List, Dict, Any, Optional

from src.core.interfaces import SearchInterface, SearchResult
from src.core.exceptions import SearchException
from src.core.config import WorkflowConfig


logger = logging.getLogger(__name__)


class TavilyAdapter(SearchInterface):
    """Adapter for Tavily search that implements SearchInterface"""
    
    def __init__(self, config: WorkflowConfig):
        self.config = config
        self._client = None
    
    def _get_client(self):
        """Get or create Tavily client"""
        if self._client is None:
            try:
                from tavily import TavilyClient
                self._client = TavilyClient(api_key=self.config.tavily_api_key)
            except ImportError:
                raise SearchException("Tavily client not available. Install with: pip install tavily-python")
            except Exception as e:
                raise SearchException(f"Failed to initialize Tavily client: {e}")
        return self._client
    
    async def search(self, query: str, **kwargs) -> List[SearchResult]:
        """Search for information using Tavily"""
        if not self.config.tavily_api_key:
            logger.warning("Tavily API key not configured, returning empty results")
            return []
        
        try:
            client = self._get_client()
            
            # Use default search criteria from config
            search_params = {
                "query": query,
                "search_depth": "basic",
                "max_results": self.config.max_search_count,
                **kwargs
            }
            
            response = client.search(**search_params)
            
            results = []
            for item in response.get("results", []):
                result = SearchResult(
                    title=item.get("title", ""),
                    content=item.get("content", ""),
                    url=item.get("url"),
                    metadata={
                        "score": item.get("score"),
                        "published_date": item.get("published_date")
                    }
                )
                results.append(result)
            
            logger.info(f"Tavily search returned {len(results)} results for query: {query}")
            return results
            
        except Exception as e:
            logger.error(f"Tavily search failed for query '{query}': {e}")
            raise SearchException(f"Search failed: {e}")
    
    async def find_collection(self, topic: str) -> Optional[Any]:
        """Find or create a collection for the given topic"""
        # Tavily doesn't have collections, so we just return None
        logger.debug(f"Collection lookup not supported for Tavily (topic: {topic})")
        return None


class MockSearchAdapter(SearchInterface):
    """Mock search adapter for testing"""
    
    def __init__(self, config: WorkflowConfig):
        self.config = config
    
    async def search(self, query: str, **kwargs) -> List[SearchResult]:
        """Return mock search results"""
        logger.info(f"Mock search for query: {query}")
        
        return [
            SearchResult(
                title=f"Mock Result for {query}",
                content=f"This is mock content related to {query}. "
                       f"It provides relevant information for policy analysis.",
                url="https://example.com/mock-result",
                metadata={"source": "mock", "relevance": 0.9}
            )
        ]
    
    async def find_collection(self, topic: str) -> Optional[Any]:
        """Mock collection lookup"""
        logger.debug(f"Mock collection lookup for topic: {topic}")
        return None
