"""
Abstract interfaces for the policy decision workflow components.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, AsyncContextManager
from dataclasses import dataclass


@dataclass
class LLMResponse:
    """Standardized response from LLM operations"""
    text: str
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class SearchResult:
    """Standardized search result"""
    title: str
    content: str
    url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class WorkflowEvent:
    """Event published during workflow execution"""
    event_type: str
    node_name: str
    data: Any
    timestamp: float


class LLMInterface(ABC):
    """Abstract interface for Language Model implementations"""
    
    @abstractmethod
    async def complete(self, prompt: str, **kwargs) -> LLMResponse:
        """Generate completion for the given prompt"""
        pass
    
    @abstractmethod
    async def chat(self, messages: List[Dict[str, str]]) -> LLMResponse:
        """Generate chat response for the given messages"""
        pass
    
    @abstractmethod
    async def __aenter__(self) -> 'LLMInterface':
        """Async context manager entry"""
        pass
    
    @abstractmethod
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit"""
        pass


class SearchInterface(ABC):
    """Abstract interface for search implementations"""
    
    @abstractmethod
    async def search(self, query: str, **kwargs) -> List[SearchResult]:
        """Search for information using the given query"""
        pass
    
    @abstractmethod
    async def find_collection(self, topic: str) -> Optional[Any]:
        """Find or create a collection for the given topic"""
        pass


class EventBusInterface(ABC):
    """Abstract interface for event publishing"""
    
    @abstractmethod
    async def publish(self, event: WorkflowEvent) -> None:
        """Publish an event to all subscribers"""
        pass
    
    @abstractmethod
    def subscribe(self, event_type: str, handler) -> None:
        """Subscribe to events of a specific type"""
        pass


class ProgressTrackerInterface(ABC):
    """Abstract interface for tracking workflow progress"""
    
    @abstractmethod
    async def update_progress(self, message: str, percentage: Optional[float] = None) -> None:
        """Update progress with a message and optional percentage"""
        pass
    
    @abstractmethod
    def get_progress_messages(self) -> List[str]:
        """Get all progress messages"""
        pass


class StorageInterface(ABC):
    """Abstract interface for data storage"""
    
    @abstractmethod
    async def store(self, key: str, data: Any) -> None:
        """Store data with the given key"""
        pass
    
    @abstractmethod
    async def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve data by key"""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete data by key"""
        pass


class ReportFormatterInterface(ABC):
    """Abstract interface for report formatting"""
    
    @abstractmethod
    async def format_report(self, report: Any) -> str:
        """Format a report into the final output format"""
        pass
    
    @abstractmethod
    async def generate_chart_data(self, report: Any) -> Dict[str, Any]:
        """Generate chart data from report content"""
        pass
