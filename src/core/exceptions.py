"""
Custom exception types for the policy decision workflow.
"""

from typing import Optional, Any


class WorkflowException(Exception):
    """Base exception for all workflow-related errors"""
    
    def __init__(self, message: str, context: Optional[dict] = None):
        super().__init__(message)
        self.context = context or {}


class LLMException(WorkflowException):
    """Exception raised when LLM operations fail"""
    pass


class SearchException(WorkflowException):
    """Exception raised when search operations fail"""
    pass


class ValidationException(WorkflowException):
    """Exception raised when data validation fails"""
    pass


class ConfigurationException(WorkflowException):
    """Exception raised when configuration is invalid"""
    pass


class NodeExecutionException(WorkflowException):
    """Exception raised when a workflow node fails to execute"""
    
    def __init__(self, node_name: str, message: str, context: Optional[dict] = None):
        super().__init__(f"Node '{node_name}' failed: {message}", context)
        self.node_name = node_name


class ReportGenerationException(WorkflowException):
    """Exception raised when report generation fails"""
    pass


class PromptException(WorkflowException):
    """Exception raised when prompt formatting or processing fails"""
    pass
