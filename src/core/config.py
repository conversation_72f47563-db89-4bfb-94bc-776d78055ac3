"""
Centralized configuration management for the policy decision workflow.
"""

from dataclasses import dataclass
from typing import Optional
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


@dataclass
class WorkflowConfig:
    """Centralized configuration for the entire workflow"""
    
    # LLM Configuration
    deepseek_api_key: str
    deepseek_model: str = "deepseek-chat"
    deepseek_reasoner_model: str = "deepseek-reasoner"
    max_tokens: int = 2048
    temperature: float = 0.7
    
    # Search Configuration
    tavily_api_key: Optional[str] = None
    query_generation_count: int = 1
    max_search_count: int = 1
    
    # Workflow Configuration
    max_retries: int = 2
    retry_wait: int = 5
    
    # Progress Messages
    service_unavailable_message: str = "服务当前不可用,请稍后再试..."
    
    # Validation
    def __post_init__(self):
        if not self.deepseek_api_key:
            raise ValueError("DEEPSEEK_API_KEY is required")
    
    @classmethod
    def from_env(cls) -> 'WorkflowConfig':
        """Create configuration from environment variables"""
        return cls(
            deepseek_api_key=os.getenv('DEEPSEEK_API_KEY', ''),
            tavily_api_key=os.getenv('TAVILY_API_KEY'),
            query_generation_count=int(os.getenv('QUERY_GENERATION_COUNT', '1')),
            max_search_count=int(os.getenv('MAX_SEARCH_COUNT', '1')),
            max_retries=int(os.getenv('MAX_RETRIES', '2')),
            retry_wait=int(os.getenv('RETRY_WAIT', '5'))
        )
    
    @classmethod
    def for_testing(cls) -> 'WorkflowConfig':
        """Create configuration for testing with minimal settings"""
        return cls(
            deepseek_api_key="test-key",
            max_retries=1,
            retry_wait=0,
            query_generation_count=1,
            max_search_count=1
        )


@dataclass
class NodeConfig:
    """Configuration specific to individual nodes"""
    
    # Progress message templates
    planning_start_message: str = "### 开始生成报告计划 \n"
    sections_start_message: str = "\n ### 开始生成报告章节 \n"
    sections_complete_message: str = "### 所有章节生成完成 \n\n"
    formatting_start_message: str = "### 格式化最终报告 \n"
    formatting_complete_message: str = "### 报告生成完成! \n"
    
    # Report formatting
    report_title: str = "# 研究报告\n\n"
    
    # Search criteria
    tavily_search_criteria: dict = None
    
    def __post_init__(self):
        if self.tavily_search_criteria is None:
            self.tavily_search_criteria = {
                "search_depth": "basic",
                "max_results": 1
            }
