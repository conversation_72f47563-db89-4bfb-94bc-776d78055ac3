"""
Node for identifying and classifying user queries.
"""

from typing import Dict, Any

from src.workflow.nodes.base import Sync<PERSON>orkflowNode, AsyncWorkflowNode
from src.core.config import WorkflowConfig, NodeConfig
from src.core.interfaces import EventBusInterface, ProgressTrackerInterface
from src.domain.services import QueryClassificationService
from src.domain.models import QueryType


class IdentifyQuestionNode(SyncWorkflowNode):
    """Node for identifying whether a query is research or chat"""
    
    def __init__(
        self,
        classification_service: QueryClassificationService,
        config: WorkflowConfig,
        node_config: NodeConfig = None,
        event_bus: EventBusInterface = None,
        progress_tracker: ProgressTrackerInterface = None
    ):
        super().__init__(config, node_config, event_bus, progress_tracker)
        self.classification_service = classification_service
    
    def prep(self, shared: Dict[str, Any]) -> str:
        """Prepare the query for classification"""
        state = self._get_workflow_state(shared)
        query = state.query.strip()
        
        self.logger.info(f"Preparing to classify query: {query}")
        return query
    
    def exec(self, query: str) -> str:
        """Execute query classification"""
        try:
            # For sync execution, we'll use a simple heuristic
            # In a real implementation, this would call the async service
            if any(keyword in query.lower() for keyword in [
                "政策", "分析", "研究", "报告", "策略", "建议", "评估", "影响"
            ]):
                return "research"
            else:
                return "chat"
                
        except Exception as e:
            self.logger.error(f"Query classification failed: {e}")
            # Default to chat for safety
            return "chat"
    
    def post(self, shared: Dict[str, Any], prep_result: str, exec_result: str) -> str:
        """Process classification result"""
        state = self._get_workflow_state(shared)
        
        if exec_result == "research":
            state.set_query_type(QueryType.RESEARCH)
            self.logger.info("Query classified as research")
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "research"
        else:
            state.set_query_type(QueryType.CHAT)
            state.set_result("这看起来是一个聊天查询，而不是研究请求。")
            self.logger.info("Query classified as chat")
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "end"


class AsyncIdentifyQuestionNode(AsyncWorkflowNode):
    """Async version of the identify question node"""
    
    def __init__(
        self,
        classification_service: QueryClassificationService,
        config: WorkflowConfig,
        node_config: NodeConfig = None,
        event_bus: EventBusInterface = None,
        progress_tracker: ProgressTrackerInterface = None
    ):
        super().__init__(config, node_config, event_bus, progress_tracker)
        self.classification_service = classification_service
    
    async def prep_async(self, shared: Dict[str, Any]) -> str:
        """Prepare the query for classification"""
        state = self._get_workflow_state(shared)
        query = state.query.strip()
        
        await self._publish_event("node_started", {"query": query})
        await self._update_progress("开始分析查询类型...")
        
        self.logger.info(f"Preparing to classify query: {query}")
        return query
    
    async def exec_async(self, query: str) -> QueryType:
        """Execute query classification using the service"""
        try:
            return await self._execute_with_monitoring(
                "query_classification",
                self.classification_service.classify_query,
                query
            )
        except Exception as e:
            self.logger.error(f"Query classification failed: {e}")
            # Default to chat for safety
            return QueryType.CHAT
    
    async def post_async(self, shared: Dict[str, Any], prep_result: str, exec_result: QueryType) -> str:
        """Process classification result"""
        state = self._get_workflow_state(shared)
        
        if exec_result == QueryType.RESEARCH:
            state.set_query_type(QueryType.RESEARCH)
            await self._update_progress("查询已分类为研究类型")
            await self._publish_event("query_classified", {"type": "research"})
            
            self.logger.info("Query classified as research")
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "research"
        else:
            state.set_query_type(QueryType.CHAT)
            state.set_result("这看起来是一个聊天查询，而不是研究请求。")
            
            await self._update_progress("查询已分类为聊天类型")
            await self._publish_event("query_classified", {"type": "chat"})
            
            self.logger.info("Query classified as chat")
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "end"
