"""
Node for generating section content.
"""

from typing import Dict, Any

from src.workflow.nodes.base import AsyncWorkflowNode
from src.core.config import WorkflowConfig, NodeConfig
from src.core.interfaces import EventBusInterface, ProgressTrackerInterface
from src.domain.services import ContentGenerationService
from src.domain.models import Report, SectionStatus


class GenerateSectionsNode(AsyncWorkflowNode):
    """Node for generating content for report sections"""
    
    def __init__(
        self,
        content_service: ContentGenerationService,
        config: WorkflowConfig,
        node_config: NodeConfig = None,
        event_bus: EventBusInterface = None,
        progress_tracker: ProgressTrackerInterface = None
    ):
        super().__init__(config, node_config, event_bus, progress_tracker)
        self.content_service = content_service
    
    async def prep_async(self, shared: Dict[str, Any]) -> Report:
        """Prepare for section generation"""
        state = self._get_workflow_state(shared)
        report = state.report
        
        if not report:
            raise ValueError("No report found in workflow state")
        
        # Add progress message
        progress_msg = self.node_config.sections_start_message
        state.add_progress_message(progress_msg)
        
        await self._publish_event("sections_started", {
            "total_sections": len(report.sections),
            "research_sections": len(report.get_research_sections())
        })
        await self._update_progress("开始生成报告章节内容...")
        
        self.logger.info(f"Starting section generation for {len(report.sections)} sections")
        return report
    
    async def exec_async(self, report: Report) -> Report:
        """Execute section content generation"""
        try:
            total_sections = len(report.sections)
            
            for i, section in enumerate(report.sections):
                progress_percentage = (i / total_sections) * 100
                
                await self._update_progress(
                    f"正在生成第 {i+1}/{total_sections} 个章节: {section.name}",
                    progress_percentage
                )
                
                await self._publish_event("section_started", {
                    "section_name": section.name,
                    "section_index": i,
                    "total_sections": total_sections
                })
                
                # Generate content for this section
                content = await self._execute_with_monitoring(
                    f"section_generation_{section.name}",
                    self.content_service.generate_section_content,
                    section,
                    self.config.query_generation_count
                )
                
                # Update section with generated content
                section.mark_completed(content)
                
                await self._publish_event("section_completed", {
                    "section_name": section.name,
                    "content_length": len(content)
                })
                
                self.logger.info(f"Generated content for section: {section.name}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"Section generation failed: {e}")
            raise
    
    async def post_async(self, shared: Dict[str, Any], prep_result: Report, exec_result: Report) -> str:
        """Process section generation result"""
        state = self._get_workflow_state(shared)
        
        # Count completed sections
        completed_sections = exec_result.get_sections_by_status(SectionStatus.COMPLETED)
        failed_sections = exec_result.get_sections_by_status(SectionStatus.FAILED)
        
        if completed_sections:
            state.set_report(exec_result)
            
            # Add completion message
            completion_msg = self.node_config.sections_complete_message
            state.add_progress_message(completion_msg)
            
            self.logger.info(f"Section generation completed: {len(completed_sections)} successful, {len(failed_sections)} failed")
            
            await self._update_progress(f"章节生成完成: {len(completed_sections)} 个成功")
            await self._publish_event("sections_completed", {
                "completed_count": len(completed_sections),
                "failed_count": len(failed_sections),
                "total_count": len(exec_result.sections)
            })
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "format_report"
        else:
            self.logger.error("All sections failed to generate")
            state.set_result(self.config.service_unavailable_message)
            
            await self._update_progress("所有章节生成失败")
            await self._publish_event("sections_failed", {"reason": "all_sections_failed"})
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "end"
