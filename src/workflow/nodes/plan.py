"""
Node for generating report plans.
"""

from typing import Dict, Any

from src.workflow.nodes.base import AsyncWorkflowNode
from src.core.config import WorkflowConfig, NodeConfig
from src.core.interfaces import EventBusInterface, ProgressTrackerInterface
from src.domain.services import ReportPlanningService
from src.domain.models import Report


class GenerateReportPlanNode(AsyncWorkflowNode):
    """Node for generating structured report plans"""
    
    def __init__(
        self,
        planning_service: ReportPlanningService,
        config: WorkflowConfig,
        node_config: NodeConfig = None,
        event_bus: EventBusInterface = None,
        progress_tracker: ProgressTrackerInterface = None
    ):
        super().__init__(config, node_config, event_bus, progress_tracker)
        self.planning_service = planning_service
    
    async def prep_async(self, shared: Dict[str, Any]) -> str:
        """Prepare for report planning"""
        state = self._get_workflow_state(shared)
        query = state.query
        
        # Add progress message
        progress_msg = self.node_config.planning_start_message
        state.add_progress_message(progress_msg)
        
        await self._publish_event("planning_started", {"query": query})
        await self._update_progress("开始生成报告计划...")
        
        self.logger.info(f"Starting report planning for query: {query}")
        return query
    
    async def exec_async(self, query: str) -> Report:
        """Execute report planning"""
        try:
            return await self._execute_with_monitoring(
                "report_planning",
                self.planning_service.generate_report_plan,
                query
            )
        except Exception as e:
            self.logger.error(f"Report planning failed: {e}")
            raise
    
    async def post_async(self, shared: Dict[str, Any], prep_result: str, exec_result: Report) -> str:
        """Process planning result"""
        state = self._get_workflow_state(shared)
        
        if exec_result and exec_result.sections:
            state.set_report(exec_result)
            
            # Log section details
            section_names = [section.name for section in exec_result.sections]
            self.logger.info(f"Generated report plan with {len(exec_result.sections)} sections: {section_names}")
            
            await self._update_progress(f"报告计划已生成，包含 {len(exec_result.sections)} 个章节")
            await self._publish_event("plan_generated", {
                "sections_count": len(exec_result.sections),
                "sections": section_names
            })
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "generate_sections"
        else:
            self.logger.error("Failed to generate valid report plan")
            state.set_result(self.config.service_unavailable_message)
            
            await self._update_progress("报告计划生成失败")
            await self._publish_event("planning_failed", {"reason": "empty_plan"})
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "end"
