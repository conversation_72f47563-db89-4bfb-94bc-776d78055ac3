"""
Node for formatting final reports.
"""

from typing import Dict, Any

from src.workflow.nodes.base import AsyncWorkflowNode
from src.core.config import WorkflowConfig, NodeConfig
from src.core.interfaces import EventBusInterface, ProgressTrackerInterface
from src.domain.services import ReportFormattingService
from src.domain.models import Report, ChartData


class FormatReportNode(AsyncWorkflowNode):
    """Node for formatting the final report and generating chart data"""
    
    def __init__(
        self,
        formatting_service: ReportFormattingService,
        config: WorkflowConfig,
        node_config: NodeConfig = None,
        event_bus: EventBusInterface = None,
        progress_tracker: ProgressTrackerInterface = None
    ):
        super().__init__(config, node_config, event_bus, progress_tracker)
        self.formatting_service = formatting_service
    
    async def prep_async(self, shared: Dict[str, Any]) -> Report:
        """Prepare for report formatting"""
        state = self._get_workflow_state(shared)
        report = state.report
        
        if not report:
            raise ValueError("No report found in workflow state")
        
        # Add progress message
        progress_msg = self.node_config.formatting_start_message
        state.add_progress_message(progress_msg)
        
        await self._publish_event("formatting_started", {
            "total_sections": len(report.sections),
            "completed_sections": report.completed_sections
        })
        await self._update_progress("开始格式化最终报告...")
        
        self.logger.info(f"Starting report formatting for {len(report.sections)} sections")
        return report
    
    async def exec_async(self, report: Report) -> Dict[str, Any]:
        """Execute report formatting and chart generation"""
        try:
            # Format the report
            await self._update_progress("正在格式化报告内容...", 25)
            formatted_report = await self._execute_with_monitoring(
                "report_formatting",
                self.formatting_service.format_report,
                report
            )
            
            # Generate chart data
            await self._update_progress("正在生成图表数据...", 75)
            chart_data = await self._execute_with_monitoring(
                "chart_generation",
                self.formatting_service.generate_chart_data,
                report
            )
            
            return {
                "formatted_report": formatted_report,
                "chart_data": chart_data
            }
            
        except Exception as e:
            self.logger.error(f"Report formatting failed: {e}")
            raise
    
    async def post_async(self, shared: Dict[str, Any], prep_result: Report, exec_result: Dict[str, Any]) -> str:
        """Process formatting result"""
        state = self._get_workflow_state(shared)
        
        formatted_report = exec_result.get("formatted_report")
        chart_data = exec_result.get("chart_data")
        
        if formatted_report:
            # Set the final results
            state.set_result(formatted_report)
            state.set_formatted_report(formatted_report)
            
            if chart_data and isinstance(chart_data, ChartData):
                state.set_chart_data(chart_data.to_dict())
            
            # Add completion message
            completion_msg = self.node_config.formatting_complete_message
            state.add_progress_message(completion_msg)
            
            self.logger.info(f"Report formatting completed. Report length: {len(formatted_report)} characters")
            
            await self._update_progress("报告格式化完成!", 100)
            await self._publish_event("formatting_completed", {
                "report_length": len(formatted_report),
                "has_chart_data": chart_data is not None
            })
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "end"
        else:
            self.logger.error("Report formatting failed - no formatted report generated")
            state.set_result(self.config.service_unavailable_message)
            
            await self._update_progress("报告格式化失败")
            await self._publish_event("formatting_failed", {"reason": "no_formatted_report"})
            
            # Update shared state
            self._update_shared_from_state(shared, state)
            
            return "end"
