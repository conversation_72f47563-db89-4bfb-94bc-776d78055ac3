"""
Base classes for workflow nodes with common functionality.
"""

import logging
import time
from typing import Dict, Any, Optional
from pocketflow import Node, AsyncNode

from src.core.config import WorkflowConfig, NodeConfig
from src.core.interfaces import EventBusInterface, ProgressTrackerInterface
from src.core.exceptions import NodeExecutionException
from src.domain.models import WorkflowState


logger = logging.getLogger(__name__)


class BaseWorkflowNode(AsyncNode):
    """Base class for all workflow nodes with common functionality"""
    
    def __init__(
        self, 
        config: WorkflowConfig,
        node_config: Optional[NodeConfig] = None,
        event_bus: Optional[EventBusInterface] = None,
        progress_tracker: Optional[ProgressTrackerInterface] = None
    ):
        super().__init__(max_retries=config.max_retries, wait=config.retry_wait)
        self.config = config
        self.node_config = node_config or NodeConfig()
        self.event_bus = event_bus
        self.progress_tracker = progress_tracker
        self.logger = logging.getLogger(self.__class__.__name__)
        self.node_name = self.__class__.__name__
    
    async def _publish_event(self, event_type: str, data: Any) -> None:
        """Publish event if event bus is available"""
        if self.event_bus:
            from src.core.interfaces import WorkflowEvent
            event = WorkflowEvent(
                event_type=event_type,
                node_name=self.node_name,
                data=data,
                timestamp=time.time()
            )
            await self.event_bus.publish(event)
    
    async def _update_progress(self, message: str, percentage: Optional[float] = None) -> None:
        """Update progress if tracker is available"""
        if self.progress_tracker:
            await self.progress_tracker.update_progress(message, percentage)
    
    async def _handle_error(self, error: Exception, context: str) -> str:
        """Centralized error handling with proper logging and events"""
        error_message = f"Error in {self.node_name}.{context}: {error}"
        self.logger.error(error_message)
        
        await self._publish_event("node_error", {
            "context": context,
            "error": str(error),
            "error_type": type(error).__name__
        })
        
        # Determine error action based on exception type
        from src.core.exceptions import LLMException, SearchException, ValidationException
        
        if isinstance(error, LLMException):
            return "llm_error"
        elif isinstance(error, SearchException):
            return "search_error"
        elif isinstance(error, ValidationException):
            return "validation_error"
        else:
            return "system_error"
    
    def _get_workflow_state(self, shared: Dict[str, Any]) -> WorkflowState:
        """Get or create workflow state from shared data"""
        if "_workflow_state" not in shared:
            # Create new workflow state
            query = shared.get("query", "")
            shared["_workflow_state"] = WorkflowState(query=query)
        
        return shared["_workflow_state"]
    
    def _update_shared_from_state(self, shared: Dict[str, Any], state: WorkflowState) -> None:
        """Update shared data from workflow state"""
        shared["_workflow_state"] = state
        shared["query"] = state.query
        shared["progress_messages"] = state.progress_messages
        
        if state.query_type:
            shared["query_type"] = state.query_type.value
        if state.report:
            shared["report"] = state.report
        if state.result:
            shared["result"] = state.result
        if state.chart_data:
            shared["chart_data"] = state.chart_data
        if state.formatted_report:
            shared["formatted_report"] = state.formatted_report
    
    async def _execute_with_monitoring(self, operation_name: str, operation_func, *args, **kwargs):
        """Execute an operation with monitoring and error handling"""
        start_time = time.time()
        
        try:
            await self._publish_event("operation_started", {"operation": operation_name})
            
            result = await operation_func(*args, **kwargs)
            
            duration = time.time() - start_time
            self.logger.info(f"{operation_name} completed in {duration:.2f}s")
            
            await self._publish_event("operation_completed", {
                "operation": operation_name,
                "duration": duration
            })
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(f"{operation_name} failed after {duration:.2f}s: {e}")
            
            await self._publish_event("operation_failed", {
                "operation": operation_name,
                "duration": duration,
                "error": str(e)
            })
            
            raise NodeExecutionException(self.node_name, f"{operation_name} failed: {e}")


class SyncWorkflowNode(Node):
    """Base class for synchronous workflow nodes"""

    def __init__(
        self,
        config: WorkflowConfig,
        node_config: Optional[NodeConfig] = None,
        event_bus: Optional[EventBusInterface] = None,
        progress_tracker: Optional[ProgressTrackerInterface] = None
    ):
        super().__init__(max_retries=config.max_retries, wait=config.retry_wait)
        self.config = config
        self.node_config = node_config or NodeConfig()
        self.event_bus = event_bus
        self.progress_tracker = progress_tracker
        self.logger = logging.getLogger(self.__class__.__name__)
        self.node_name = self.__class__.__name__

    def prep(self, shared: Dict[str, Any]) -> Any:
        """Synchronous preparation - to be overridden by subclasses"""
        return shared.get("query", "")

    def exec(self, prep_result: Any) -> Any:
        """Synchronous execution - to be overridden by subclasses"""
        return prep_result

    def post(self, shared: Dict[str, Any], prep_result: Any, exec_result: Any) -> str:
        """Synchronous post-processing - to be overridden by subclasses"""
        return "end"


class AsyncWorkflowNode(BaseWorkflowNode):
    """Base class for asynchronous workflow nodes"""
    
    async def prep_async(self, shared: Dict[str, Any]) -> Any:
        """Asynchronous preparation - to be overridden by subclasses"""
        return shared.get("query", "")
    
    async def exec_async(self, prep_result: Any) -> Any:
        """Asynchronous execution - to be overridden by subclasses"""
        return prep_result
    
    async def post_async(self, shared: Dict[str, Any], prep_result: Any, exec_result: Any) -> str:
        """Asynchronous post-processing - to be overridden by subclasses"""
        return "end"
