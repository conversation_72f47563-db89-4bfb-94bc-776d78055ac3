"""
Factory for creating workflow components and orchestrating the complete workflow.
"""

from typing import Optional
from pocketflow import Async<PERSON>low

from src.core.config import WorkflowConfig, NodeConfig
from src.core.interfaces import EventBusInterface, ProgressTrackerInterface
from src.infrastructure.llm.factory import LLMFactory
from src.infrastructure.search.factory import SearchFactory
from src.domain.services import (
    QueryClassificationService,
    ReportPlanningService,
    ContentGenerationService,
    ReportFormattingService
)
from src.workflow.nodes.identify import AsyncIdentifyQuestionNode
from src.workflow.nodes.plan import GenerateReportPlanNode
from src.workflow.nodes.sections import GenerateSectionsNode
from src.workflow.nodes.format import FormatReportNode


class WorkflowFactory:
    """Factory for creating workflow components"""
    
    def __init__(
        self,
        config: WorkflowConfig,
        node_config: Optional[NodeConfig] = None,
        event_bus: Optional[EventBusInterface] = None,
        progress_tracker: Optional[ProgressTrackerInterface] = None
    ):
        self.config = config
        self.node_config = node_config or NodeConfig()
        self.event_bus = event_bus
        self.progress_tracker = progress_tracker
        
        # Create infrastructure components
        self.llm = LLMFactory.create_llm(config)
        self.search = SearchFactory.create_search_client(config)
        
        # Create services
        self.classification_service = QueryClassificationService(self.llm)
        self.planning_service = ReportPlanningService(self.llm, self.search)
        self.content_service = ContentGenerationService(self.llm, self.search)
        self.formatting_service = ReportFormattingService(self.llm)
    
    def create_identify_node(self) -> AsyncIdentifyQuestionNode:
        """Create the question identification node"""
        return AsyncIdentifyQuestionNode(
            classification_service=self.classification_service,
            config=self.config,
            node_config=self.node_config,
            event_bus=self.event_bus,
            progress_tracker=self.progress_tracker
        )
    
    def create_plan_node(self) -> GenerateReportPlanNode:
        """Create the report planning node"""
        return GenerateReportPlanNode(
            planning_service=self.planning_service,
            config=self.config,
            node_config=self.node_config,
            event_bus=self.event_bus,
            progress_tracker=self.progress_tracker
        )
    
    def create_sections_node(self) -> GenerateSectionsNode:
        """Create the sections generation node"""
        return GenerateSectionsNode(
            content_service=self.content_service,
            config=self.config,
            node_config=self.node_config,
            event_bus=self.event_bus,
            progress_tracker=self.progress_tracker
        )
    
    def create_format_node(self) -> FormatReportNode:
        """Create the report formatting node"""
        return FormatReportNode(
            formatting_service=self.formatting_service,
            config=self.config,
            node_config=self.node_config,
            event_bus=self.event_bus,
            progress_tracker=self.progress_tracker
        )
    
    def create_workflow(self) -> AsyncFlow:
        """Create the complete workflow with all nodes connected"""
        
        # Create nodes
        identify_node = self.create_identify_node()
        plan_node = self.create_plan_node()
        sections_node = self.create_sections_node()
        format_node = self.create_format_node()
        
        # Connect nodes
        identify_node - "research" >> plan_node
        plan_node - "generate_sections" >> sections_node
        sections_node - "format_report" >> format_node
        
        # Create and return the flow
        return AsyncFlow(start=identify_node)


class SimpleWorkflowFactory:
    """Simplified factory for basic workflow creation"""
    
    @staticmethod
    def create_from_config(config: WorkflowConfig) -> AsyncFlow:
        """Create a workflow from configuration only"""
        factory = WorkflowFactory(config)
        return factory.create_workflow()
    
    @staticmethod
    def create_from_env() -> AsyncFlow:
        """Create a workflow from environment variables"""
        config = WorkflowConfig.from_env()
        return SimpleWorkflowFactory.create_from_config(config)
    
    @staticmethod
    def create_for_testing() -> AsyncFlow:
        """Create a workflow for testing with minimal configuration"""
        config = WorkflowConfig.for_testing()
        return SimpleWorkflowFactory.create_from_config(config)
