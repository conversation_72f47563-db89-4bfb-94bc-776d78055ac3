"""
Domain models for the policy decision workflow.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class QueryType(str, Enum):
    """Types of queries the system can handle"""
    RESEARCH = "research"
    CHAT = "chat"


class SectionStatus(str, Enum):
    """Status of a report section"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class Section(BaseModel):
    """Domain model for a report section"""
    name: str = Field(..., description="Name of the section")
    description: str = Field(..., description="Description of what this section covers")
    research: bool = Field(..., description="Whether this section requires research")
    content: str = Field(default="", description="The actual content of the section")
    status: SectionStatus = Field(default=SectionStatus.PENDING, description="Current status")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Additional metadata")
    
    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('Section name cannot be empty')
        return v.strip()
    
    @validator('description')
    def description_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('Section description cannot be empty')
        return v.strip()
    
    def mark_in_progress(self):
        """Mark section as in progress"""
        self.status = SectionStatus.IN_PROGRESS
    
    def mark_completed(self, content: str):
        """Mark section as completed with content"""
        self.content = content
        self.status = SectionStatus.COMPLETED
    
    def mark_failed(self, error_message: str):
        """Mark section as failed"""
        self.status = SectionStatus.FAILED
        self.metadata["error"] = error_message


class Report(BaseModel):
    """Domain model for a complete policy report"""
    sections: List[Section] = Field(..., description="List of report sections")
    title: Optional[str] = Field(default=None, description="Report title")
    query: Optional[str] = Field(default=None, description="Original query that generated this report")
    created_at: datetime = Field(default_factory=datetime.now, description="When the report was created")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional report metadata")
    
    @validator('sections')
    def sections_must_not_be_empty(cls, v):
        if not v:
            raise ValueError('Report must have at least one section')
        return v
    
    @property
    def total_sections(self) -> int:
        """Total number of sections"""
        return len(self.sections)
    
    @property
    def completed_sections(self) -> int:
        """Number of completed sections"""
        return len([s for s in self.sections if s.status == SectionStatus.COMPLETED])
    
    @property
    def progress_percentage(self) -> float:
        """Progress as percentage"""
        if self.total_sections == 0:
            return 0.0
        return (self.completed_sections / self.total_sections) * 100
    
    @property
    def is_complete(self) -> bool:
        """Whether all sections are completed"""
        return all(s.status == SectionStatus.COMPLETED for s in self.sections)
    
    def get_sections_by_status(self, status: SectionStatus) -> List[Section]:
        """Get sections by their status"""
        return [s for s in self.sections if s.status == status]
    
    def get_research_sections(self) -> List[Section]:
        """Get sections that require research"""
        return [s for s in self.sections if s.research]
    
    def get_content_sections(self) -> List[Section]:
        """Get sections that don't require research"""
        return [s for s in self.sections if not s.research]


class WorkflowState(BaseModel):
    """State of the workflow execution"""
    query: str = Field(..., description="Original user query")
    query_type: Optional[QueryType] = Field(default=None, description="Classified query type")
    report: Optional[Report] = Field(default=None, description="Generated report")
    result: Optional[str] = Field(default=None, description="Final result")
    progress_messages: List[str] = Field(default_factory=list, description="Progress messages")
    chart_data: Dict[str, Any] = Field(default_factory=dict, description="Chart data for visualization")
    formatted_report: Optional[str] = Field(default=None, description="Final formatted report")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional workflow metadata")
    
    def add_progress_message(self, message: str):
        """Add a progress message"""
        self.progress_messages.append(message)
    
    def set_query_type(self, query_type: QueryType):
        """Set the classified query type"""
        self.query_type = query_type
    
    def set_report(self, report: Report):
        """Set the generated report"""
        self.report = report
    
    def set_result(self, result: str):
        """Set the final result"""
        self.result = result
    
    def set_chart_data(self, chart_data: Dict[str, Any]):
        """Set chart data"""
        self.chart_data = chart_data
    
    def set_formatted_report(self, formatted_report: str):
        """Set the formatted report"""
        self.formatted_report = formatted_report


class ChartData(BaseModel):
    """Model for chart data visualization"""
    bar_chart: Optional[Dict[str, Any]] = Field(default=None, description="Bar chart data")
    line_chart: Optional[Dict[str, Any]] = Field(default=None, description="Line chart data")
    pie_chart: Optional[Dict[str, Any]] = Field(default=None, description="Pie chart data")
    radar_chart: Optional[Dict[str, Any]] = Field(default=None, description="Radar chart data")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values"""
        return {k: v for k, v in self.dict().items() if v is not None}
