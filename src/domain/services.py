"""
Business logic services for the policy decision workflow.
"""

import json
import logging
from typing import List, Dict, Any, Optional

from src.core.interfaces import LLMInterface, SearchInterface
from src.core.exceptions import LLMException, SearchException, ValidationException
from src.domain.models import Report, Section, QueryType, ChartData
import prompts


logger = logging.getLogger(__name__)


class QueryClassificationService:
    """Service for classifying user queries"""
    
    def __init__(self, llm: LLMInterface):
        self.llm = llm
    
    async def classify_query(self, query: str) -> QueryType:
        """Classify a query as research or chat"""
        try:
            messages = [
                {"role": "system", "content": prompts.agent_prompt},
                {"role": "user", "content": query}
            ]
            
            response = await self.llm.chat(messages)
            
            response_text = response.text.strip().lower()
            if "research" in response_text:
                return QueryType.RESEARCH
            else:
                return QueryType.CHAT
                
        except Exception as e:
            logger.error(f"Failed to classify query: {e}")
            raise LLMException(f"Query classification failed: {e}")


class ReportPlanningService:
    """Service for generating report plans"""
    
    def __init__(self, llm: LLMInterface, search: Optional[SearchInterface] = None):
        self.llm = llm
        self.search = search
    
    async def generate_report_plan(self, query: str) -> Report:
        """Generate a structured report plan based on the query"""
        try:
            # Initialize search collection if available
            if self.search:
                await self.search.find_collection(topic=query)
            
            # Generate report structure
            prompt = prompts.report_planner_instructions.format(topic=query)
            response = await self.llm.complete(prompt, response_format={"type": "json_object"})
            
            # Parse and validate response
            report_data = self._parse_report_structure(response.text)
            report = self._create_report_from_data(report_data, query)
            
            logger.info(f"Generated report plan with {len(report.sections)} sections")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate report plan: {e}")
            raise LLMException(f"Report planning failed: {e}")
    
    def _parse_report_structure(self, response_text: str) -> Dict[str, Any]:
        """Parse LLM response into structured data"""
        try:
            data = json.loads(response_text)
            if "report" not in data:
                raise ValidationException("Response missing 'report' key")
            return data["report"]
        except json.JSONDecodeError as e:
            raise ValidationException(f"Invalid JSON response: {e}")
    
    def _create_report_from_data(self, report_data: Dict[str, Any], query: str) -> Report:
        """Create Report object from parsed data"""
        try:
            all_sections = []
            
            # Add introduction
            if "introduction" in report_data:
                all_sections.append(report_data["introduction"])
            
            # Add main body sections
            if "mainBodySections" in report_data:
                all_sections.extend(report_data["mainBodySections"])
            
            # Add conclusion
            if "conclusion" in report_data:
                all_sections.append(report_data["conclusion"])
            
            # Create Section objects
            sections = [Section(**section_data) for section_data in all_sections]
            
            return Report(
                sections=sections,
                query=query,
                title=f"Policy Analysis: {query}"
            )
            
        except Exception as e:
            raise ValidationException(f"Failed to create report structure: {e}")


class ContentGenerationService:
    """Service for generating section content"""
    
    def __init__(self, llm: LLMInterface, search: Optional[SearchInterface] = None):
        self.llm = llm
        self.search = search
    
    async def generate_section_content(self, section: Section, query_count: int = 1) -> str:
        """Generate content for a specific section"""
        if not section.research:
            return section.content
        
        try:
            section.mark_in_progress()
            
            # Generate search queries
            queries = await self._generate_search_queries(section.description, query_count)
            logger.info(f"Generated {len(queries)} search queries for section: {section.name}")
            
            # Perform searches
            search_results = await self._perform_searches(queries)
            
            # Generate content using search context
            content = await self._generate_content_from_context(section.description, search_results)
            
            section.mark_completed(content)
            logger.info(f"Generated content for section: {section.name}")
            
            return content
            
        except Exception as e:
            section.mark_failed(str(e))
            logger.error(f"Failed to generate content for section {section.name}: {e}")
            raise SearchException(f"Content generation failed for section '{section.name}': {e}")
    
    async def _generate_search_queries(self, topic: str, count: int) -> List[str]:
        """Generate search queries for the given topic"""
        try:
            prompt = prompts.query_generator_instructions.format(topic=topic, count=count)
            response = await self.llm.complete(prompt, response_format={"type": "json_object"})
            
            parsed_data = json.loads(response.text)
            queries = parsed_data.get("queries", [topic])
            
            return queries[:count] if len(queries) > count else queries
            
        except Exception as e:
            logger.warning(f"Failed to generate search queries, using topic as fallback: {e}")
            return [topic]
    
    async def _perform_searches(self, queries: List[str]) -> str:
        """Perform searches and aggregate results"""
        if not self.search:
            return "No search results available (search client not configured)"
        
        search_results = []
        
        for query in queries:
            try:
                results = await self.search.search(query)
                if results:
                    formatted_results = "\n".join([
                        f"- {result.title}: {result.content}" 
                        for result in results
                    ])
                    search_results.append(f"Query: {query}\n{formatted_results}")
            except Exception as e:
                logger.warning(f"Search failed for query '{query}': {e}")
        
        return "\n\n".join(search_results) if search_results else "No search results found"
    
    async def _generate_content_from_context(self, topic: str, context: str) -> str:
        """Generate section content using search context"""
        try:
            prompt = prompts.section_writer_instructions.format(
                section_topic=topic, 
                context=context
            )
            
            response = await self.llm.complete(prompt)
            return response.text
            
        except Exception as e:
            raise LLMException(f"Failed to generate content from context: {e}")


class ReportFormattingService:
    """Service for formatting final reports"""
    
    def __init__(self, llm: LLMInterface):
        self.llm = llm
    
    async def format_report(self, report: Report) -> str:
        """Format report into final markdown format"""
        try:
            formatted_report = "# 研究报告\n\n"
            
            for section in report.sections:
                formatted_report += f"## {section.name}\n\n"
                formatted_report += f"{section.content}\n\n"
            
            logger.info(f"Formatted report with {len(report.sections)} sections")
            return formatted_report
            
        except Exception as e:
            logger.error(f"Failed to format report: {e}")
            raise ValidationException(f"Report formatting failed: {e}")
    
    async def generate_chart_data(self, report: Report) -> ChartData:
        """Generate chart data from report content"""
        try:
            # Extract all content from completed sections
            report_content = "\n".join([
                section.content 
                for section in report.sections 
                if section.content and section.status.value == "completed"
            ])
            
            if not report_content:
                logger.warning("No content available for chart generation")
                return ChartData()
            
            # Generate chart data using LLM
            prompt = prompts.chart_data_generator_instructions.format(
                report_content=report_content
            )
            
            response = await self.llm.complete(prompt, response_format={"type": "json_object"})
            chart_data_dict = json.loads(response.text)
            
            chart_data = ChartData(**chart_data_dict)
            logger.info("Generated chart data successfully")
            
            return chart_data
            
        except Exception as e:
            logger.error(f"Failed to generate chart data: {e}")
            return ChartData()  # Return empty chart data instead of failing
