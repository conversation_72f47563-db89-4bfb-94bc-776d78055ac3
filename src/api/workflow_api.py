"""
Public API interface for the policy decision workflow.
"""

import logging
from typing import Dict, Any, Optional

from src.core.config import WorkflowConfig, NodeConfig
from src.core.exceptions import WorkflowException, ConfigurationException
from src.domain.models import WorkflowState
from src.workflow.factory import WorkflowFactory


logger = logging.getLogger(__name__)


class PolicyDecisionWorkflowAPI:
    """Public API for the policy decision workflow"""
    
    def __init__(
        self,
        config: Optional[WorkflowConfig] = None,
        node_config: Optional[NodeConfig] = None
    ):
        """
        Initialize the workflow API.
        
        Args:
            config: Workflow configuration. If None, loads from environment.
            node_config: Node-specific configuration. If None, uses defaults.
        """
        self.config = config or WorkflowConfig.from_env()
        self.node_config = node_config or NodeConfig()
        self.factory = WorkflowFactory(self.config, self.node_config)
        
        logger.info("Policy Decision Workflow API initialized")
    
    async def run_workflow(
        self,
        query: str,
        verbose: bool = False
    ) -> Dict[str, Any]:
        """
        Run the complete policy decision workflow.
        
        Args:
            query: The user's query to process
            verbose: Whether to include detailed progress information
            
        Returns:
            Dictionary containing the workflow results
            
        Raises:
            WorkflowException: If the workflow fails
            ConfigurationException: If configuration is invalid
        """
        try:
            # Validate input
            if not query or not query.strip():
                raise WorkflowException("Query cannot be empty")
            
            # Create workflow
            workflow = self.factory.create_workflow()
            
            # Initialize workflow state
            initial_state = {
                "query": query.strip(),
                "progress_messages": [],
                "verbose": verbose
            }
            
            logger.info(f"Starting workflow for query: {query}")
            
            # Run the workflow
            await workflow.run_async(initial_state)
            
            # Extract results
            result = self._extract_results(initial_state)
            
            logger.info("Workflow completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Workflow failed: {e}")
            if isinstance(e, WorkflowException):
                raise
            else:
                raise WorkflowException(f"Workflow execution failed: {e}")
    
    def _extract_results(self, shared_state: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and format results from the shared state"""
        
        # Get workflow state if available
        workflow_state = shared_state.get("_workflow_state")
        
        result = {
            "query": shared_state.get("query", ""),
            "query_type": shared_state.get("query_type"),
            "result": shared_state.get("result"),
            "progress_messages": shared_state.get("progress_messages", []),
            "success": shared_state.get("result") is not None
        }
        
        # Add optional fields if available
        if "chart_data" in shared_state:
            result["chart_data"] = shared_state["chart_data"]
        
        if "formatted_report" in shared_state:
            result["formatted_report"] = shared_state["formatted_report"]
        
        if "report" in shared_state:
            report = shared_state["report"]
            if hasattr(report, 'sections'):
                result["sections_count"] = len(report.sections)
                result["completed_sections"] = report.completed_sections
                result["progress_percentage"] = report.progress_percentage
        
        # Add metadata
        result["metadata"] = {
            "config": {
                "max_retries": self.config.max_retries,
                "query_generation_count": self.config.query_generation_count,
                "max_search_count": self.config.max_search_count
            }
        }
        
        return result


# Convenience functions for backward compatibility
async def run_policy_decision_workflow(
    query: str,
    llm=None,  # Deprecated - use config instead
    tavily_api_key: Optional[str] = None,
    verbose: bool = False,
    config: Optional[WorkflowConfig] = None
) -> Dict[str, Any]:
    """
    Convenience function to run the policy decision workflow.
    
    This function provides backward compatibility with the original API.
    
    Args:
        query: The user's query to process
        llm: Deprecated - LLM configuration is now handled by WorkflowConfig
        tavily_api_key: Optional Tavily API key (overrides config)
        verbose: Whether to include detailed progress information
        config: Optional workflow configuration
        
    Returns:
        Dictionary containing the workflow results
    """
    
    # Create config if not provided
    if config is None:
        config = WorkflowConfig.from_env()
        
        # Override tavily key if provided
        if tavily_api_key:
            config.tavily_api_key = tavily_api_key
    
    # Create and run workflow
    api = PolicyDecisionWorkflowAPI(config)
    return await api.run_workflow(query, verbose)


def create_policy_decision_flow(
    llm=None,  # Deprecated
    tavily_api_key: Optional[str] = None,
    verbose: bool = False,
    config: Optional[WorkflowConfig] = None
):
    """
    Create a policy decision workflow flow.
    
    This function provides backward compatibility with the original API.
    
    Args:
        llm: Deprecated - LLM configuration is now handled by WorkflowConfig
        tavily_api_key: Optional Tavily API key
        verbose: Whether to enable verbose logging
        config: Optional workflow configuration
        
    Returns:
        AsyncFlow instance
    """
    
    # Create config if not provided
    if config is None:
        config = WorkflowConfig.from_env()
        
        # Override tavily key if provided
        if tavily_api_key:
            config.tavily_api_key = tavily_api_key
    
    # Create factory and workflow
    factory = WorkflowFactory(config)
    return factory.create_workflow()
