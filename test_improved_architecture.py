#!/usr/bin/env python3
"""
Test suite for the improved policy decision workflow architecture.
"""

import sys
import os
import asyncio
import pytest
from unittest.mock import Mock, AsyncMock

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.config import WorkflowConfig, NodeConfig
from src.core.exceptions import WorkflowException, LLMException
from src.domain.models import WorkflowState, QueryType, Report, Section
from src.api.workflow_api import PolicyDecisionWorkflowAPI


class TestImprovedArchitecture:
    """Test suite for the improved architecture"""
    
    def setup_method(self):
        """Setup test configuration"""
        self.config = WorkflowConfig.for_testing()
        self.node_config = NodeConfig()
    
    def test_config_creation(self):
        """Test configuration creation and validation"""
        # Test valid config
        config = WorkflowConfig(deepseek_api_key="test-key")
        assert config.deepseek_api_key == "test-key"
        assert config.max_retries == 2
        
        # Test invalid config
        with pytest.raises(ValueError):
            WorkflowConfig(deepseek_api_key="")
    
    def test_workflow_state(self):
        """Test workflow state management"""
        state = WorkflowState(query="test query")
        
        # Test initial state
        assert state.query == "test query"
        assert state.query_type is None
        assert state.report is None
        
        # Test state updates
        state.set_query_type(QueryType.RESEARCH)
        assert state.query_type == QueryType.RESEARCH
        
        state.add_progress_message("Test message")
        assert len(state.progress_messages) == 1
        assert state.progress_messages[0] == "Test message"
    
    def test_report_model(self):
        """Test report domain model"""
        sections = [
            Section(name="Introduction", description="Intro", research=True),
            Section(name="Conclusion", description="Conclusion", research=False)
        ]
        
        report = Report(sections=sections)
        
        # Test properties
        assert report.total_sections == 2
        assert report.completed_sections == 0
        assert report.progress_percentage == 0.0
        assert not report.is_complete
        
        # Test section completion
        sections[0].mark_completed("Introduction content")
        assert report.completed_sections == 1
        assert report.progress_percentage == 50.0
        
        sections[1].mark_completed("Conclusion content")
        assert report.completed_sections == 2
        assert report.progress_percentage == 100.0
        assert report.is_complete
    
    async def test_api_initialization(self):
        """Test API initialization"""
        api = PolicyDecisionWorkflowAPI(self.config, self.node_config)
        
        assert api.config == self.config
        assert api.node_config == self.node_config
        assert api.factory is not None
    
    async def test_api_validation(self):
        """Test API input validation"""
        api = PolicyDecisionWorkflowAPI(self.config, self.node_config)
        
        # Test empty query
        with pytest.raises(WorkflowException, match="Query cannot be empty"):
            await api.run_workflow("")
        
        # Test whitespace-only query
        with pytest.raises(WorkflowException, match="Query cannot be empty"):
            await api.run_workflow("   ")


async def test_workflow_integration():
    """Integration test for the complete workflow"""
    print("\n=== Integration Test ===")
    
    try:
        # Check if we have required environment variables
        if not os.getenv('DEEPSEEK_API_KEY'):
            print("Skipping integration test - DEEPSEEK_API_KEY not set")
            return
        
        # Create API with real configuration
        config = WorkflowConfig.from_env()
        api = PolicyDecisionWorkflowAPI(config)
        
        # Test with a simple query
        result = await api.run_workflow(
            query="中国新能源政策简要分析",
            verbose=True
        )
        
        # Verify results
        assert result["success"] is True
        assert result["query"] == "中国新能源政策简要分析"
        assert result["query_type"] in ["research", "chat"]
        assert len(result["progress_messages"]) > 0
        
        print(f"✓ Integration test passed")
        print(f"  Query type: {result['query_type']}")
        print(f"  Progress messages: {len(result['progress_messages'])}")
        print(f"  Has result: {'result' in result and result['result'] is not None}")
        
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_error_handling():
    """Test error handling in the workflow"""
    print("\n=== Error Handling Test ===")
    
    try:
        # Test with invalid configuration
        config = WorkflowConfig(deepseek_api_key="invalid-key")
        api = PolicyDecisionWorkflowAPI(config)
        
        # This should fail gracefully
        result = await api.run_workflow("test query")
        
        # Should not reach here, but if it does, check for error handling
        print(f"Unexpected success: {result}")
        return False
        
    except WorkflowException as e:
        print(f"✓ Error handling test passed - caught WorkflowException: {e}")
        return True
    except Exception as e:
        print(f"✗ Error handling test failed - unexpected exception: {e}")
        return False


async def main():
    """Run all tests"""
    print("Testing Improved Policy Decision Workflow Architecture")
    print("=" * 60)
    
    # Run unit tests
    print("\n=== Unit Tests ===")
    test_suite = TestImprovedArchitecture()
    test_suite.setup_method()
    
    try:
        test_suite.test_config_creation()
        print("✓ Config creation test passed")
        
        test_suite.test_workflow_state()
        print("✓ Workflow state test passed")
        
        test_suite.test_report_model()
        print("✓ Report model test passed")
        
        await test_suite.test_api_initialization()
        print("✓ API initialization test passed")
        
        await test_suite.test_api_validation()
        print("✓ API validation test passed")
        
    except Exception as e:
        print(f"✗ Unit test failed: {e}")
        return False
    
    # Run integration tests
    integration_success = await test_workflow_integration()
    error_handling_success = await test_error_handling()
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("✓ Unit tests: PASSED")
    print(f"{'✓' if integration_success else '✗'} Integration test: {'PASSED' if integration_success else 'FAILED'}")
    print(f"{'✓' if error_handling_success else '✗'} Error handling test: {'PASSED' if error_handling_success else 'FAILED'}")
    
    overall_success = integration_success and error_handling_success
    print(f"\n🎉 Overall: {'ALL TESTS PASSED' if overall_success else 'SOME TESTS FAILED'}")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
