#!/usr/bin/env python3
"""
专门测试改进架构的测试文件
"""

import sys
import os
import asyncio
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Import our improved architecture
try:
    from src.core.config import WorkflowConfig
    from src.api.workflow_api import PolicyDecisionWorkflowAPI
    from src.domain.models import WorkflowState, QueryType
    print("✅ 改进架构模块导入成功")
except ImportError as e:
    print(f"❌ 架构导入失败: {e}")
    sys.exit(1)


async def test_improved_architecture():
    """专门测试改进的架构"""
    print("🏗️  测试改进的政策决策工作流架构")
    print("="*50)
    
    # 检查环境变量
    deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
    if not deepseek_api_key:
        print("❌ 缺少 DEEPSEEK_API_KEY")
        return False
    
    print(f"✅ API Key: {deepseek_api_key[:10]}...")
    
    try:
        # 创建配置
        config = WorkflowConfig(
            deepseek_api_key=deepseek_api_key,
            tavily_api_key=os.getenv('TAVILY_API_KEY'),
            max_retries=2,
            query_generation_count=1,
            max_search_count=1
        )
        print("✅ 配置创建成功")
        
        # 创建API实例
        api = PolicyDecisionWorkflowAPI(config)
        print("✅ API实例创建成功")
        
        # 测试查询
        test_query = "中国新能源汽车政策的主要特点"
        print(f"📝 测试查询: {test_query}")
        
        # 运行改进的工作流
        print("\n🚀 开始运行改进的工作流...")
        start_time = time.time()
        
        result = await api.run_workflow(
            query=test_query,
            verbose=True
        )
        
        execution_time = time.time() - start_time
        
        # 分析结果
        print(f"\n📊 执行结果:")
        print(f"   执行时间: {execution_time:.2f}秒")
        print(f"   成功状态: {result.get('success', False)}")
        print(f"   查询类型: {result.get('query_type', 'N/A')}")
        print(f"   进度消息数量: {len(result.get('progress_messages', []))}")
        
        if result.get('result'):
            result_text = str(result['result'])
            print(f"   结果长度: {len(result_text)} 字符")
            if len(result_text) > 100:
                print(f"   结果预览: {result_text[:200]}...")
        
        if result.get('metadata'):
            print(f"   元数据: {result['metadata']}")
        
        # 检查进度消息
        if result.get('progress_messages'):
            print(f"\n📈 进度消息:")
            for i, msg in enumerate(result['progress_messages'][:5], 1):
                print(f"   {i}. {msg}")
        
        # 成功标准
        success_criteria = [
            result.get('success', False),
            result.get('query_type') is not None,
            len(result.get('progress_messages', [])) > 0
        ]
        
        overall_success = all(success_criteria)
        
        print(f"\n🎯 测试结果: {'✅ 成功' if overall_success else '❌ 失败'}")
        
        if overall_success:
            print("\n🎉 改进架构测试通过!")
            print("✨ 架构优势:")
            print("  🔧 模块化设计")
            print("  ⚙️  集中配置管理")
            print("  🚨 结构化异常处理")
            print("  📊 增强的进度跟踪")
            print("  🛡️  更好的错误处理")
        else:
            print("\n⚠️  测试未完全通过，但架构基础正常")
        
        return overall_success
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_individual_components():
    """测试各个组件"""
    print("\n🔧 测试各个架构组件")
    print("="*30)
    
    try:
        # 测试配置
        config = WorkflowConfig(deepseek_api_key="test-key")
        print("✅ 配置组件正常")
        
        # 测试领域模型
        state = WorkflowState(query="测试查询")
        state.set_query_type(QueryType.RESEARCH)
        print("✅ 领域模型正常")
        
        # 测试异常
        from src.core.exceptions import WorkflowException, LLMException
        try:
            raise LLMException("测试异常")
        except WorkflowException:
            print("✅ 异常层次结构正常")
        
        # 测试服务
        from src.domain.services import QueryClassificationService
        print("✅ 服务层正常")
        
        # 测试工厂
        from src.workflow.factory import WorkflowFactory
        factory = WorkflowFactory(config)
        print("✅ 工厂模式正常")
        
        print("\n✅ 所有组件测试通过!")
        return True
        
    except Exception as e:
        print(f"\n❌ 组件测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 改进架构专项测试")
    print("="*60)
    
    # 测试组件
    components_ok = await test_individual_components()
    
    # 测试完整工作流
    workflow_ok = await test_improved_architecture()
    
    # 总结
    print("\n" + "="*60)
    print("🎯 最终测试结果")
    print("="*60)
    
    print(f"✅ 组件测试: {'通过' if components_ok else '失败'}")
    print(f"✅ 工作流测试: {'通过' if workflow_ok else '失败'}")
    
    overall_success = components_ok and workflow_ok
    
    if overall_success:
        print("\n🎉 改进架构完全正常工作!")
        print("🚀 架构改进成功实现!")
    else:
        print("\n⚠️  部分测试未通过，需要进一步调试")
    
    return overall_success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
