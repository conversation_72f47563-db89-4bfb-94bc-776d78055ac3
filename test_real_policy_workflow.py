#!/usr/bin/env python3
"""
Real integration test for the policy decision workflow.
This test uses actual LLM calls to test the complete workflow end-to-end.

Usage:
    python test_real_policy_workflow.py

Requirements:
    - DEEPSEEK_API_KEY environment variable must be set
    - TAVILY_API_KEY environment variable (optional, for web search)
"""

import sys
import os
import asyncio
from dotenv import load_dotenv

# Add project root to Python path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

# Load environment variables
load_dotenv()

from policy_decision_workflow_pocketflow import (
    run_policy_decision_workflow,
    IdentifyQuestionNode,
    GenerateReportPlanNode
)
from llama_index.llms.deepseek import DeepSeek

def create_test_llm():
    """Create a test LLM instance"""
    # Use DeepSeek as it's more reliable for this test
    api_key = os.environ.get("DEEPSEEK_API_KEY")
    if not api_key:
        raise Exception("DEEPSEEK_API_KEY is required for this test")

    print("Using DeepSeek for testing")
    return DeepSeek(
        api_key=api_key,
        model="deepseek-chat",
        max_tokens=2048
    )

async def test_identify_question_node_real():
    """Test the IdentifyQuestionNode with real LLM"""
    print("\n=== Testing IdentifyQuestionNode ===")
    
    llm = create_test_llm()
    thinking_llm = llm  # Use same LLM for thinking
    
    node = IdentifyQuestionNode(llm, thinking_llm)
    
    # Test research query
    shared = {"query": "中国新能源汽车政策的发展趋势如何？"}
    
    print(f"Input query: {shared['query']}")
    
    prep_result = node.prep(shared)
    exec_result = node.exec(prep_result)
    post_result = node.post(shared, prep_result, exec_result)
    
    print(f"LLM response: {exec_result}")
    print(f"Classification result: {post_result}")
    print(f"Query type in shared: {shared.get('query_type', 'N/A')}")
    
    return post_result == "research"

async def test_generate_report_plan_real():
    """Test the GenerateReportPlanNode with real LLM"""
    print("\n=== Testing GenerateReportPlanNode ===")
    
    llm = create_test_llm()
    
    # Mock RAG client since we don't have a real one set up
    class MockRAGClient:
        async def find_collection(self, topic):
            print(f"Mock RAG: Finding collection for topic: {topic}")
            return None
    
    rag_client = MockRAGClient()
    node = GenerateReportPlanNode(llm, rag_client)
    
    shared = {
        "query": "中国新能源汽车政策",
        "progress_messages": []
    }
    
    print(f"Input query: {shared['query']}")
    
    prep_result = await node.prep_async(shared)
    print(f"Generated prompt length: {len(prep_result)} characters")
    
    exec_result = await node.exec_async(prep_result)
    print(f"LLM response type: {type(exec_result)}")
    if exec_result:
        print(f"Report structure keys: {list(exec_result.keys())}")
    
    post_result = await node.post_async(shared, prep_result, exec_result)
    print(f"Post processing result: {post_result}")
    
    if "report" in shared:
        report = shared["report"]
        print(f"Generated report with {len(report.sections)} sections:")
        for i, section in enumerate(report.sections):
            print(f"  {i+1}. {section.name}: {section.description}")
    
    return post_result == "generate_sections" and "report" in shared

async def test_complete_workflow_real():
    """Test the complete workflow with real LLM"""
    print("\n=== Testing Complete Workflow ===")
    
    llm = create_test_llm()
    tavily_api_key = os.environ.get("TAVILY_API_KEY")
    
    query = "中国碳中和政策的实施策略"
    print(f"Testing complete workflow with query: {query}")
    
    try:
        result = await run_policy_decision_workflow(
            query=query,
            llm=llm,
            tavily_api_key=tavily_api_key,
            verbose=True
        )
        
        print(f"Workflow completed successfully!")
        print(f"Result keys: {list(result.keys())}")
        
        if "result" in result and result["result"]:
            print(f"Final result length: {len(result['result'])} characters")
            print(f"Result preview: {result['result'][:200]}...")
        
        if "progress_messages" in result:
            print(f"Progress messages count: {len(result['progress_messages'])}")
            for i, msg in enumerate(result["progress_messages"]):
                print(f"  Progress {i+1}: {msg[:100]}...")
        
        if "chart_data" in result:
            print(f"Chart data keys: {list(result['chart_data'].keys())}")
        
        return True
        
    except Exception as e:
        print(f"Workflow failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_individual_nodes():
    """Test individual nodes separately"""
    print("\n=== Testing Individual Nodes ===")
    
    success_count = 0
    total_tests = 2
    
    # Test IdentifyQuestionNode
    try:
        if await test_identify_question_node_real():
            print("✓ IdentifyQuestionNode test passed")
            success_count += 1
        else:
            print("✗ IdentifyQuestionNode test failed")
    except Exception as e:
        print(f"✗ IdentifyQuestionNode test error: {e}")
    
    # Test GenerateReportPlanNode
    try:
        if await test_generate_report_plan_real():
            print("✓ GenerateReportPlanNode test passed")
            success_count += 1
        else:
            print("✗ GenerateReportPlanNode test failed")
    except Exception as e:
        print(f"✗ GenerateReportPlanNode test error: {e}")
    
    print(f"\nIndividual node tests: {success_count}/{total_tests} passed")
    return success_count == total_tests

async def main():
    """Main test function"""
    print("Starting Real Policy Decision Workflow Tests")
    print("=" * 60)
    
    # Check environment variables
    required_vars = ["DEEPSEEK_API_KEY"]
    optional_vars = ["TAVILY_API_KEY"]
    
    print("Environment check:")
    for var in required_vars:
        if os.environ.get(var):
            print(f"✓ {var} is set")
        else:
            print(f"✗ {var} is missing (required)")
            return False
    
    for var in optional_vars:
        if os.environ.get(var):
            print(f"✓ {var} is set")
        else:
            print(f"- {var} is not set (optional)")
    
    print()
    
    # Run tests
    individual_success = await test_individual_nodes()
    complete_success = await test_complete_workflow_real()
    
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print(f"Individual nodes: {'✓ PASSED' if individual_success else '✗ FAILED'}")
    print(f"Complete workflow: {'✓ PASSED' if complete_success else '✗ FAILED'}")
    
    if individual_success and complete_success:
        print("\n🎉 All tests passed! The workflow is working correctly.")
        return True
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
