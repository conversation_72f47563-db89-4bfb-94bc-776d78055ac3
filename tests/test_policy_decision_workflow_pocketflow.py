import sys
import os
import pytest
import asyncio
from unittest.mock import MagicMock, patch, AsyncMock
import json

# 添加项目根目录到 Python 路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from policy_decision_workflow_pocketflow import (
    IdentifyQuestionNode, 
    GenerateReportPlanNode, 
    GenerateSectionsNode, 
    FormatReportNode,
    create_policy_decision_flow,
    run_policy_decision_workflow
)
from models import Report, Section
from llama_index.core.llms import ChatMessage, ChatResponse

# 创建模拟的 LLM 类
class MockLLM:
    def __init__(self, chat_response=None, complete_response=None):
        self.chat_response = chat_response or "research"
        self.complete_response = complete_response or {"text": "{}"}
        
    def chat(self, messages):
        return ChatResponse(message=ChatMessage(role="assistant", content=self.chat_response))
    
    def complete(self, prompt, response_format=None):
        if isinstance(self.complete_response, dict) and "text" in self.complete_response:
            return MagicMock(**self.complete_response)
        return MagicMock(text=self.complete_response)

# 创建模拟的异步 LLM 类
class MockAsyncLLM:
    def __init__(self, chat_response=None, complete_response=None):
        self.chat_response = chat_response or "research"
        self.complete_response = complete_response or {"text": "{}"}
        
    async def chat(self, messages):
        return ChatResponse(message=ChatMessage(role="assistant", content=self.chat_response))
    
    async def complete(self, prompt, response_format=None):
        if isinstance(self.complete_response, dict) and "text" in self.complete_response:
            return MagicMock(**self.complete_response)
        return MagicMock(text=self.complete_response)

# 单元测试：IdentifyQuestionNode
@pytest.mark.asyncio
async def test_identify_question_node_research():
    """测试识别研究类型查询"""
    # 创建模拟的 LLM
    llm = MockLLM(chat_response="research")
    thinking_llm = MockLLM(chat_response="research")
    
    # 创建节点
    node = IdentifyQuestionNode(llm, thinking_llm)
    
    # 准备共享状态
    shared = {"query": "中国新能源政策发展趋势如何？"}
    
    # 执行节点
    prep_result = node.prep(shared)
    exec_result = node.exec(prep_result)
    post_result = node.post(shared, prep_result, exec_result)
    
    # 验证结果
    assert post_result == "research"
    assert shared["query_type"] == "research"

@pytest.mark.asyncio
async def test_identify_question_node_chat():
    """测试识别聊天类型查询"""
    # 创建模拟的 LLM
    llm = MockLLM(chat_response="这是一个聊天回复")
    thinking_llm = MockLLM(chat_response="这是一个聊天回复")
    
    # 创建节点
    node = IdentifyQuestionNode(llm, thinking_llm)
    
    # 准备共享状态
    shared = {"query": "你好，请问今天天气怎么样？"}
    
    # 执行节点
    prep_result = node.prep(shared)
    exec_result = node.exec(prep_result)
    post_result = node.post(shared, prep_result, exec_result)
    
    # 验证结果
    assert post_result == "end"
    assert shared["result"] == "这是一个聊天回复"

# 单元测试：GenerateReportPlanNode
@pytest.mark.asyncio
async def test_generate_report_plan_node():
    """测试生成报告计划"""
    # 创建模拟的 LLM
    report_json = {
        "report": {
            "introduction": {
                "name": "引言",
                "description": "中国新能源政策概述",
                "research": True,
                "content": ""
            },
            "mainBodySections": [
                {
                    "name": "政策背景",
                    "description": "中国新能源政策的历史背景和发展",
                    "research": True,
                    "content": ""
                }
            ],
            "conclusion": {
                "name": "结论",
                "description": "中国新能源政策未来展望",
                "research": True,
                "content": ""
            }
        }
    }
    llm = MockAsyncLLM(complete_response={"text": json.dumps(report_json)})
    
    # 创建模拟的 RAG 客户端
    rag_client = MagicMock()
    rag_client.find_collection = AsyncMock(return_value=None)
    
    # 创建节点
    node = GenerateReportPlanNode(llm, rag_client)
    
    # 修改 exec_async 方法以使用模拟响应
    async def mock_exec_async(prompt):
        return report_json
    
    node.exec_async = mock_exec_async
    
    # 准备共享状态
    shared = {"query": "中国新能源政策"}
    
    # 执行节点
    prep_result = await node.prep_async(shared)
    exec_result = await node.exec_async(prep_result)
    post_result = await node.post_async(shared, prep_result, exec_result)
    
    # 验证结果
    assert post_result == "generate_sections"
    assert isinstance(shared["report"], Report)
    assert len(shared["report"].sections) == 3
    assert "progress_messages" in shared

# 单元测试：GenerateSectionsNode
@pytest.mark.asyncio
async def test_generate_sections_node():
    """测试生成报告章节"""
    # 创建模拟的 LLM
    llm = MockAsyncLLM(complete_response="这是章节内容")
    
    # 创建模拟的 RAG 客户端和 Tavily 客户端
    rag_client = MagicMock()
    rag_client.search = AsyncMock(return_value="RAG搜索结果")
    tavily_client = MagicMock()
    tavily_client.search = AsyncMock(return_value={"results": [{"title": "标题", "content": "内容"}]})
    
    # 创建节点
    node = GenerateSectionsNode(llm, rag_client, tavily_client)
    
    # 模拟 generate_queries 和 perform_searches 方法
    node.generate_queries = AsyncMock(return_value=["查询1"])
    node.perform_searches = AsyncMock(return_value="搜索结果")
    
    # 修改 exec_async 方法以使用模拟响应
    async def mock_exec_async(report):
        for section in report.sections:
            section.content = "这是章节内容"
        return report
    
    node.exec_async = mock_exec_async
    
    # 准备共享状态和报告
    sections = [
        Section(name="引言", description="描述", research=True, content=""),
        Section(name="主体", description="描述", research=True, content=""),
        Section(name="结论", description="描述", research=True, content="")
    ]
    report = Report(sections=sections)
    shared = {"report": report, "progress_messages": []}
    
    # 执行节点
    prep_result = await node.prep_async(shared)
    exec_result = await node.exec_async(prep_result)
    post_result = await node.post_async(shared, prep_result, exec_result)
    
    # 验证结果
    assert post_result == "format_report"
    assert all(section.content for section in shared["report"].sections)
    assert "progress_messages" in shared

# 单元测试：FormatReportNode
def test_format_report_node():
    """测试格式化最终报告"""
    # 创建模拟的 LLM
    chart_data = {"bar_chart": {}, "line_chart": {}, "pie_chart": {}, "radar_chart": {}}
    llm = MockLLM(complete_response=json.dumps(chart_data))
    
    # 创建节点
    node = FormatReportNode(llm)
    
    # 模拟 generate_chart_data 方法
    node.generate_chart_data = MagicMock(return_value=chart_data)
    
    # 准备共享状态和报告
    sections = [
        Section(name="引言", description="描述", content="引言内容", research=True),
        Section(name="主体", description="描述", content="主体内容", research=True),
        Section(name="结论", description="描述", content="结论内容", research=True)
    ]
    report = Report(sections=sections)
    shared = {"report": report, "progress_messages": []}
    
    # 执行节点
    prep_result = node.prep(shared)
    exec_result = node.exec(prep_result)
    post_result = node.post(shared, prep_result, exec_result)
    
    # 验证结果
    assert post_result == "end"
    assert "formatted_report" in shared
    assert "chart_data" in shared
    assert "progress_messages" in shared

# 集成测试：完整流程
@pytest.mark.asyncio
async def test_create_policy_decision_flow():
    """测试创建政策决策流程"""
    # 创建模拟的 LLM
    llm = MockLLM()
    
    # 创建流程
    flow = create_policy_decision_flow(llm)
    
    # 验证流程结构
    assert flow.start is not None

@pytest.mark.asyncio
async def test_run_policy_decision_workflow_research():
    """测试运行政策决策工作流（研究型查询）"""
    # 创建模拟的 Flow 类
    mock_flow = MagicMock()
    mock_flow.run_async = AsyncMock()
    
    # 使用 patch 模拟 create_policy_decision_flow 函数
    with patch('policy_decision_workflow_pocketflow.create_policy_decision_flow', return_value=mock_flow):
        # 运行工作流
        result = await run_policy_decision_workflow("中国新能源政策", MockLLM())
        
        # 验证 Flow.run_async 被调用
        mock_flow.run_async.assert_called_once()
        
        # 验证结果
        assert "result" in result
        assert "progress_messages" in result
        assert "chart_data" in result

@pytest.mark.asyncio
async def test_run_policy_decision_workflow_chat():
    """测试运行政策决策工作流（聊天型查询）"""
    # 创建模拟的 Flow 类
    mock_flow = MagicMock()
    mock_flow.run_async = AsyncMock()
    
    # 使用 patch 模拟 create_policy_decision_flow 函数
    with patch('policy_decision_workflow_pocketflow.create_policy_decision_flow', return_value=mock_flow):
        # 运行工作流
        result = await run_policy_decision_workflow("你好，请问今天天气怎么样？", MockLLM())
        
        # 验证 Flow.run_async 被调用
        mock_flow.run_async.assert_called_once()
        
        # 验证结果
        assert "result" in result
        assert "progress_messages" in result
        assert "chart_data" in result

if __name__ == "__main__":
    pytest.main(["-xvs", "test_policy_decision_workflow_pocketflow.py"])