#!/usr/bin/env python3
"""
Real test to verify the improved architecture works with actual API calls.
This test validates the complete workflow end-to-end with real LLM and search services.
"""

import sys
import os
import asyncio
import time
import json
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.core.config import WorkflowConfig, NodeConfig
from src.core.exceptions import WorkflowException, LLMException, SearchException
from src.api.workflow_api import PolicyDecisionWorkflowAPI
from src.workflow.factory import WorkflowFactory
from src.domain.models import QueryType, SectionStatus


class ArchitectureVerificationTest:
    """Comprehensive test suite for the improved architecture"""
    
    def __init__(self):
        self.config = WorkflowConfig.from_env()
        self.node_config = NodeConfig()
        self.test_results = []
    
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "success": success,
            "details": details
        })
        print(f"{status} {test_name}")
        if details:
            print(f"    {details}")
    
    async def test_configuration_validation(self):
        """Test configuration management and validation"""
        print("\n=== Testing Configuration Management ===")
        
        try:
            # Test valid configuration
            config = WorkflowConfig.from_env()
            assert config.deepseek_api_key, "DeepSeek API key should be loaded"
            self.log_test_result("Configuration Loading", True, f"API key loaded: {config.deepseek_api_key[:10]}...")
            
            # Test configuration validation
            try:
                invalid_config = WorkflowConfig(deepseek_api_key="")
                self.log_test_result("Configuration Validation", False, "Should have failed with empty API key")
            except ValueError:
                self.log_test_result("Configuration Validation", True, "Correctly rejected empty API key")
            
            # Test custom configuration
            custom_config = WorkflowConfig(
                deepseek_api_key="test-key",
                max_retries=5,
                query_generation_count=3
            )
            assert custom_config.max_retries == 5
            self.log_test_result("Custom Configuration", True, "Custom values set correctly")
            
        except Exception as e:
            self.log_test_result("Configuration Management", False, str(e))
    
    async def test_llm_adapter(self):
        """Test LLM adapter functionality"""
        print("\n=== Testing LLM Adapter ===")
        
        try:
            from src.infrastructure.llm.factory import LLMFactory
            
            # Create LLM instance
            llm = LLMFactory.create_llm(self.config)
            self.log_test_result("LLM Factory Creation", True, "LLM instance created successfully")
            
            # Test simple completion
            async with llm as llm_instance:
                response = await llm_instance.complete("Say 'Hello, World!' in Chinese")
                assert response.text, "Response should not be empty"
                assert len(response.text) > 0, "Response should have content"
                self.log_test_result("LLM Completion", True, f"Response: {response.text[:50]}...")
                
                # Test chat functionality
                messages = [
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "What is 2+2?"}
                ]
                chat_response = await llm_instance.chat(messages)
                assert chat_response.text, "Chat response should not be empty"
                self.log_test_result("LLM Chat", True, f"Chat response: {chat_response.text[:50]}...")
        
        except Exception as e:
            self.log_test_result("LLM Adapter", False, str(e))
    
    async def test_search_adapter(self):
        """Test search adapter functionality"""
        print("\n=== Testing Search Adapter ===")
        
        try:
            from src.infrastructure.search.factory import SearchFactory
            
            # Create search client
            search_client = SearchFactory.create_search_client(self.config)
            self.log_test_result("Search Factory Creation", True, "Search client created successfully")
            
            # Test search functionality
            if self.config.tavily_api_key:
                results = await search_client.search("China renewable energy policy")
                assert isinstance(results, list), "Search should return a list"
                self.log_test_result("Real Search", True, f"Found {len(results)} results")
                
                if results:
                    first_result = results[0]
                    assert hasattr(first_result, 'title'), "Result should have title"
                    assert hasattr(first_result, 'content'), "Result should have content"
                    self.log_test_result("Search Result Structure", True, f"Title: {first_result.title[:50]}...")
            else:
                # Test mock search
                results = await search_client.search("test query")
                self.log_test_result("Mock Search", True, f"Mock search returned {len(results)} results")
        
        except Exception as e:
            self.log_test_result("Search Adapter", False, str(e))
    
    async def test_domain_models(self):
        """Test domain models and business logic"""
        print("\n=== Testing Domain Models ===")
        
        try:
            from src.domain.models import Report, Section, WorkflowState
            
            # Test Section model
            section = Section(
                name="测试章节",
                description="这是一个测试章节",
                research=True
            )
            assert section.status == SectionStatus.PENDING
            self.log_test_result("Section Creation", True, f"Section: {section.name}")
            
            # Test section state transitions
            section.mark_in_progress()
            assert section.status == SectionStatus.IN_PROGRESS
            
            section.mark_completed("测试内容已生成")
            assert section.status == SectionStatus.COMPLETED
            assert section.content == "测试内容已生成"
            self.log_test_result("Section State Transitions", True, "All transitions work correctly")
            
            # Test Report model
            sections = [
                Section(name="引言", description="政策背景", research=True),
                Section(name="分析", description="深度分析", research=True),
                Section(name="结论", description="政策建议", research=False)
            ]
            
            report = Report(sections=sections, title="测试报告")
            assert report.total_sections == 3
            assert report.progress_percentage == 0.0
            self.log_test_result("Report Creation", True, f"Report with {report.total_sections} sections")
            
            # Test progress tracking
            sections[0].mark_completed("引言内容")
            sections[1].mark_completed("分析内容")
            assert report.progress_percentage == 66.7
            self.log_test_result("Progress Tracking", True, f"Progress: {report.progress_percentage:.1f}%")
            
            # Test WorkflowState
            state = WorkflowState(query="测试查询")
            state.set_query_type(QueryType.RESEARCH)
            state.add_progress_message("测试进度消息")
            assert len(state.progress_messages) == 1
            self.log_test_result("Workflow State", True, "State management works correctly")
        
        except Exception as e:
            self.log_test_result("Domain Models", False, str(e))
    
    async def test_service_layer(self):
        """Test service layer business logic"""
        print("\n=== Testing Service Layer ===")
        
        try:
            from src.domain.services import QueryClassificationService, ReportPlanningService
            from src.infrastructure.llm.factory import LLMFactory
            from src.infrastructure.search.factory import SearchFactory
            
            # Create services
            llm = LLMFactory.create_llm(self.config)
            search = SearchFactory.create_search_client(self.config)
            
            # Test query classification
            classification_service = QueryClassificationService(llm)
            query_type = await classification_service.classify_query("中国新能源政策分析")
            assert query_type in [QueryType.RESEARCH, QueryType.CHAT]
            self.log_test_result("Query Classification", True, f"Classified as: {query_type.value}")
            
            # Test report planning
            planning_service = ReportPlanningService(llm, search)
            report = await planning_service.generate_report_plan("中国碳中和政策")
            assert report is not None
            assert len(report.sections) > 0
            self.log_test_result("Report Planning", True, f"Generated {len(report.sections)} sections")
            
            # Verify section structure
            for section in report.sections:
                assert section.name, "Section should have a name"
                assert section.description, "Section should have a description"
            self.log_test_result("Section Structure", True, "All sections have required fields")
        
        except Exception as e:
            self.log_test_result("Service Layer", False, str(e))
    
    async def test_workflow_factory(self):
        """Test workflow factory and node creation"""
        print("\n=== Testing Workflow Factory ===")
        
        try:
            # Create factory
            factory = WorkflowFactory(self.config, self.node_config)
            self.log_test_result("Factory Creation", True, "WorkflowFactory created successfully")
            
            # Test individual node creation
            identify_node = factory.create_identify_node()
            assert identify_node is not None
            self.log_test_result("Identify Node Creation", True, "Node created successfully")
            
            plan_node = factory.create_plan_node()
            assert plan_node is not None
            self.log_test_result("Plan Node Creation", True, "Node created successfully")
            
            sections_node = factory.create_sections_node()
            assert sections_node is not None
            self.log_test_result("Sections Node Creation", True, "Node created successfully")
            
            format_node = factory.create_format_node()
            assert format_node is not None
            self.log_test_result("Format Node Creation", True, "Node created successfully")
            
            # Test complete workflow creation
            workflow = factory.create_workflow()
            assert workflow is not None
            self.log_test_result("Complete Workflow Creation", True, "Workflow created and connected")
        
        except Exception as e:
            self.log_test_result("Workflow Factory", False, str(e))
    
    async def test_complete_workflow_execution(self):
        """Test complete workflow execution end-to-end"""
        print("\n=== Testing Complete Workflow Execution ===")
        
        try:
            # Create API instance
            api = PolicyDecisionWorkflowAPI(self.config, self.node_config)
            self.log_test_result("API Creation", True, "PolicyDecisionWorkflowAPI created")
            
            # Test with a research query
            start_time = time.time()
            result = await api.run_workflow(
                query="中国新能源汽车政策的主要特点",
                verbose=True
            )
            execution_time = time.time() - start_time
            
            # Verify result structure
            assert isinstance(result, dict), "Result should be a dictionary"
            assert "query" in result, "Result should contain query"
            assert "success" in result, "Result should contain success flag"
            assert "progress_messages" in result, "Result should contain progress messages"
            
            self.log_test_result("Workflow Execution", True, f"Completed in {execution_time:.2f}s")
            
            # Verify specific results
            if result["success"]:
                assert result["query_type"] in ["research", "chat"], "Should have valid query type"
                assert len(result["progress_messages"]) > 0, "Should have progress messages"
                
                if result.get("result"):
                    assert len(result["result"]) > 100, "Result should have substantial content"
                    self.log_test_result("Content Generation", True, f"Generated {len(result['result'])} characters")
                
                if result.get("sections_count"):
                    assert result["sections_count"] > 0, "Should have generated sections"
                    self.log_test_result("Section Generation", True, f"Generated {result['sections_count']} sections")
                
                if result.get("chart_data"):
                    assert isinstance(result["chart_data"], dict), "Chart data should be a dictionary"
                    self.log_test_result("Chart Data Generation", True, f"Generated chart data with {len(result['chart_data'])} charts")
            
            # Print detailed results
            print(f"\n📊 Workflow Results:")
            print(f"   Query: {result['query']}")
            print(f"   Query Type: {result.get('query_type', 'N/A')}")
            print(f"   Success: {result['success']}")
            print(f"   Progress Messages: {len(result['progress_messages'])}")
            print(f"   Execution Time: {execution_time:.2f}s")
            
            if result.get("result"):
                print(f"   Result Length: {len(result['result'])} characters")
                print(f"   Result Preview: {result['result'][:200]}...")
        
        except Exception as e:
            self.log_test_result("Complete Workflow Execution", False, str(e))
    
    async def test_error_handling(self):
        """Test error handling and recovery"""
        print("\n=== Testing Error Handling ===")
        
        try:
            # Test with invalid configuration
            invalid_config = WorkflowConfig(deepseek_api_key="invalid-key-12345")
            api = PolicyDecisionWorkflowAPI(invalid_config)
            
            try:
                result = await api.run_workflow("test query")
                # If it succeeds, check if it handled the error gracefully
                if not result["success"]:
                    self.log_test_result("Invalid API Key Handling", True, "Gracefully handled invalid API key")
                else:
                    self.log_test_result("Invalid API Key Handling", False, "Should have failed with invalid key")
            except WorkflowException:
                self.log_test_result("Invalid API Key Handling", True, "Correctly raised WorkflowException")
            
            # Test with empty query
            valid_api = PolicyDecisionWorkflowAPI(self.config)
            try:
                await valid_api.run_workflow("")
                self.log_test_result("Empty Query Handling", False, "Should have rejected empty query")
            except WorkflowException:
                self.log_test_result("Empty Query Handling", True, "Correctly rejected empty query")
        
        except Exception as e:
            self.log_test_result("Error Handling", False, str(e))
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "="*70)
        print("🧪 ARCHITECTURE VERIFICATION TEST SUMMARY")
        print("="*70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['details']}")
        
        print("\n" + "="*70)
        if failed_tests == 0:
            print("🎉 ALL TESTS PASSED! The improved architecture is working perfectly!")
        else:
            print("⚠️  Some tests failed. Please review the issues above.")
        
        return failed_tests == 0


async def main():
    """Run the complete architecture verification test"""
    print("🏗️  Policy Decision Workflow - Architecture Verification Test")
    print("="*70)
    print("This test verifies the improved architecture with real API calls.")
    print("="*70)
    
    # Check environment
    required_vars = ["DEEPSEEK_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {missing_vars}")
        print("Please set these variables in your .env file")
        return False
    
    print("✅ Environment variables configured")
    
    # Run tests
    test_suite = ArchitectureVerificationTest()
    
    await test_suite.test_configuration_validation()
    await test_suite.test_llm_adapter()
    await test_suite.test_search_adapter()
    await test_suite.test_domain_models()
    await test_suite.test_service_layer()
    await test_suite.test_workflow_factory()
    await test_suite.test_complete_workflow_execution()
    await test_suite.test_error_handling()
    
    # Print summary
    success = test_suite.print_summary()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
